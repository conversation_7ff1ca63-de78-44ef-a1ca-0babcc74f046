# Manage NuGet Packages Task

## Task Definition
- **ID**: manage-nuget-packages
- **Name**: NuGet Package Management
- **Description**: Add, update, or remove NuGet packages with best practices and security considerations
- **Agent**: csharp-dev
- **Elicit**: true

## Prerequisites
- .NET project with valid .csproj file
- Internet connection for package downloads
- Understanding of project requirements

## Elicitation Questions
1. **Action Type**: What package management action do you need?
   - Add new package
   - Update existing package
   - Remove package
   - Audit packages for vulnerabilities
   - Clean up unused packages

2. **Package Details** (if adding/updating):
   - Package name
   - Specific version (or latest)
   - Target project(s)

3. **Security Requirements**: 
   - Should we check for known vulnerabilities?
   - Any compliance requirements?
   - License compatibility checks needed?

## Execution Steps

### Step 1: Package Analysis
```bash
# List current packages
dotnet list package

# Check for outdated packages
dotnet list package --outdated

# Check for vulnerable packages
dotnet list package --vulnerable
```

### Step 2: Add Package (if applicable)
```bash
# Add package with latest version
dotnet add package {{package_name}}

# Add specific version
dotnet add package {{package_name}} --version {{version}}

# Add to specific project
dotnet add {{project_path}} package {{package_name}}
```

### Step 3: Update Packages (if applicable)
```bash
# Update specific package
dotnet add package {{package_name}}

# Update all packages in project
dotnet restore
```

### Step 4: Remove Package (if applicable)
```bash
# Remove package
dotnet remove package {{package_name}}
```

## Recommended Packages by Category

### Web Development
- `Microsoft.AspNetCore.OpenApi` - OpenAPI support
- `Swashbuckle.AspNetCore` - Swagger documentation
- `Microsoft.AspNetCore.Authentication.JwtBearer` - JWT authentication

### Data Access
- `Microsoft.EntityFrameworkCore` - ORM framework
- `Microsoft.EntityFrameworkCore.SqlServer` - SQL Server provider
- `Dapper` - Micro ORM

### Logging
- `Serilog.AspNetCore` - Structured logging
- `Microsoft.Extensions.Logging` - Built-in logging

### Testing
- `xunit` - Testing framework
- `Microsoft.NET.Test.Sdk` - Test SDK
- `Moq` - Mocking framework
- `FluentAssertions` - Assertion library

### Utilities
- `Newtonsoft.Json` - JSON serialization
- `AutoMapper` - Object mapping
- `FluentValidation` - Input validation

## Security Considerations
- Always check package vulnerabilities before adding
- Use specific version numbers for production
- Regularly update packages for security patches
- Review package licenses for compliance
- Avoid packages with suspicious or minimal maintenance

## Validation Checklist
- [ ] Package successfully installed/updated/removed
- [ ] No dependency conflicts
- [ ] Project builds successfully
- [ ] No security vulnerabilities introduced
- [ ] License compatibility verified
- [ ] Documentation updated if needed

## Output
- Updated project file with new package references
- Package lock file updated
- Security audit report (if requested)
- Recommendations for future package management
