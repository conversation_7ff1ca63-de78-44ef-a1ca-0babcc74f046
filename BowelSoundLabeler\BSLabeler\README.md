# 肠鸣音标注器 (BSLabeler)

基于WPF的肠鸣音文件标注工具，由MATLAB版本转换而来。

## 功能特性

- 🎵 **多格式支持**: 支持 .mat、.wav、.mp3、.m4a、.flac 等音频文件格式
- 📁 **智能文件夹记忆**: 自动记住上次使用的文件夹路径
- 📝 **批量处理**: 支持单个或批量文件标注
- 🏷️ **自动重命名**: 根据标注结果自动添加 `_no` 或 `_yes_N` 后缀
- ⚡ **现代UI**: 美观的WPF界面，支持拖拽操作
- 🛡️ **错误处理**: 完善的错误处理和用户提示

## 使用方法

### 1. 设置默认文件夹（可选）
- 点击 **"设置默认文件夹"** 选择常用的工作目录
- 应用会记住这个设置，下次打开时自动使用

### 2. 选择文件
- **选择单个文件**: 标注单个音频文件
- **选择多个文件**: 批量标注多个音频文件

### 3. 开始标注
- 点击 **"开始标注"** 按钮
- 对每个文件，系统会询问：
  - **是否包含肠鸣音？**
    - 选择 **"否"**: 文件重命名为 `原文件名_no.扩展名`
    - 选择 **"是"**: 输入肠鸣音数量，文件重命名为 `原文件名_yes_数量.扩展名`
    - 选择 **"取消"**: 跳过当前文件

### 4. 查看结果
- 标注完成后显示处理统计信息
- 成功处理、跳过、错误的文件数量

## 文件命名规则

- **无肠鸣音**: `example.wav` → `example_no.wav`
- **有肠鸣音**: `example.wav` → `example_yes_3.wav` (假设输入数量为3)

## 编译和运行

### 在Visual Studio 2022中：
1. 打开 `BSLabeler.sln` 文件
2. 确保项目目标框架为 .NET Framework 4.8
3. 按 `F5` 或点击 "开始调试" 运行项目

### 手动编译：
```bash
# 在开发者命令提示符中
cd "路径\到\BSLabeler"
msbuild BSLabeler.sln /p:Configuration=Release
```

## 系统要求

- Windows 10/11
- .NET Framework 4.8
- Visual Studio 2022 (用于开发)

## 项目结构

```
BSLabeler/
├── MainWindow.xaml          # 主窗口界面
├── MainWindow.xaml.cs       # 主窗口逻辑
├── InputDialog.xaml         # 输入对话框界面
├── InputDialog.xaml.cs      # 输入对话框逻辑
├── Properties/
│   └── Settings.settings    # 应用程序设置
└── BSLabeler.csproj         # 项目文件
```

## 技术特点

- **MVVM架构**: 清晰的界面和逻辑分离
- **异步处理**: 避免UI冻结，提供流畅的用户体验
- **设置持久化**: 使用 Properties.Settings 保存用户偏好
- **现代UI设计**: 使用WPF样式和资源，美观大方

## 从MATLAB版本的改进

1. **更好的用户体验**: 现代化的WPF界面
2. **更强的扩展性**: 面向对象的C#代码结构
3. **更快的性能**: 编译型语言，启动和运行更快
4. **更好的错误处理**: 更详细的异常信息和用户提示
5. **更灵活的部署**: 可以编译为独立可执行文件

## 贡献者

转换自MATLAB版本的肠鸣音标注器

