# C# 扩展包用户指南

## 概述

C# 扩展包是专为 C# 和 .NET 开发设计的 Cursor 编辑器扩展，提供了专业的 C# 开发代理、模板、任务和工作流。

## 安装信息

- **版本**: 1.0.0
- **安装日期**: 2025-08-28
- **兼容性**: 与 bmad-core 集成
- **支持的 IDE**: Cursor

## 可用代理

### 1. C# 开发者 (@csharp-dev)
**角色**: Alex - C# & .NET 开发专家  
**图标**: 🔷  
**专长**:
- C# 语言特性（最新版本）
- .NET Core/.NET 5+ 生态系统
- ASP.NET Core 开发
- Entity Framework Core
- 依赖注入模式
- 单元测试（xUnit/NUnit）
- 性能优化
- 内存管理
- 异步/等待模式

**可用命令**:
- `*help` - 显示可用的 C# 开发命令
- `*create-project` - 创建新的 C# 项目
- `*add-package` - 添加 NuGet 包
- `*code-review` - 进行 C# 代码审查
- `*test-setup` - 设置测试框架
- `*performance-check` - 性能分析
- `*refactor` - 代码重构
- `*security-scan` - 安全扫描
- `*api-design` - API 设计
- `*ef-setup` - Entity Framework 设置

### 2. .NET 架构师 (@dotnet-architect)
**角色**: Sarah - .NET 解决方案架构师  
**图标**: 🏗️  
**专长**:
- 企业级 .NET 架构模式
- 微服务架构
- Azure 云集成
- Docker 容器化
- Kubernetes 编排
- API 设计和版本控制
- 安全最佳实践
- 性能和可扩展性
- DevOps 和 CI/CD 管道

**可用命令**:
- `*help` - 显示可用的架构命令
- `*design-solution` - 创建解决方案架构
- `*microservices-plan` - 规划微服务分解
- `*api-design` - 设计 RESTful API
- `*cloud-strategy` - 云迁移/部署策略
- `*security-review` - 安全架构审查
- `*performance-plan` - 性能优化计划
- `*devops-setup` - 设置 CI/CD 管道
- `*tech-stack` - 技术栈评估
- `*integration-design` - 系统集成设计
- `*data-architecture` - 数据架构设计

## 支持的项目类型

- Console Application（控制台应用程序）
- Web API
- MVC Web Application
- Blazor Server/WebAssembly
- WPF Application
- WinForms Application
- Class Library（类库）
- Test Project（测试项目）

## 支持的 .NET 框架

- .NET 8.0（最新 LTS）
- .NET 6.0（LTS）
- .NET Standard 2.1

## 工作流

### C# Web API 开发工作流
完整的 Web API 开发流程，包括：
1. 项目初始化
2. 架构设计
3. 数据层设置
4. 业务逻辑实现
5. API 控制器开发
6. 身份验证和安全
7. 测试实现
8. 日志和监控
9. 配置管理
10. API 文档
11. 性能优化
12. 代码质量审查
13. 部署准备
14. 最终验证

## 模板

### Web API 项目模板
- 完整的分层架构项目结构
- 配置文件模板
- 依赖注入设置
- 数据库集成
- 身份验证配置

### 控制器模板
- RESTful API 控制器
- CRUD 操作
- 输入验证
- 错误处理
- 异步模式
- DTO 支持

## 检查清单

### C# 代码审查检查清单
全面的代码质量检查，包括：
- 代码质量和风格
- 性能和内存管理
- 安全性
- 错误处理
- 设计模式和架构
- 测试
- 配置和部署

## 最佳实践指南

包含以下主题的详细指南：
- 命名约定
- 代码组织
- 错误处理
- 异步/等待模式
- 内存管理
- LINQ 最佳实践
- 依赖注入
- 性能考虑
- 安全最佳实践
- 测试最佳实践
- 配置管理

## 使用示例

### 创建新的 Web API 项目
```
@csharp-dev
*create-project
```

### 进行代码审查
```
@csharp-dev
*code-review
```

### 设计微服务架构
```
@dotnet-architect
*microservices-plan
```

### 设置 Entity Framework
```
@csharp-dev
*ef-setup
```

## 故障排除

### 常见问题

1. **代理无法激活**
   - 检查文件路径是否正确
   - 确保所有必需文件都存在
   - 验证 YAML 文件格式

2. **命令无法执行**
   - 确认代理已正确激活
   - 检查依赖文件是否存在
   - 验证权限设置

3. **模板无法加载**
   - 检查模板文件路径
   - 验证模板文件格式
   - 确认模板依赖项

### 支持

如需帮助，请：
1. 检查用户指南和文档
2. 验证安装和配置
3. 查看错误日志
4. 联系技术支持

## 更新日志

### 版本 1.0.0 (2025-08-28)
- 初始版本发布
- C# 开发者代理
- .NET 架构师代理
- Web API 开发工作流
- 基础模板和检查清单
- 最佳实践指南
