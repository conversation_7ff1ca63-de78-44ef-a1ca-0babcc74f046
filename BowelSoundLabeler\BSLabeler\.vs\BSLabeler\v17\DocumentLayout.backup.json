{"Version": 1, "WorkspaceRootPath": "D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BSLabeler\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{D542786E-23B2-40FE-84FF-2F281FF987D2}|BSLabeler.csproj|d:\\phd\\code\\c#\\work\\bowelsoundlabeler\\bslabeler\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{D542786E-23B2-40FE-84FF-2F281FF987D2}|BSLabeler.csproj|solutionrelative:mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{D542786E-23B2-40FE-84FF-2F281FF987D2}|BSLabeler.csproj|D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BSLabeler\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D542786E-23B2-40FE-84FF-2F281FF987D2}|BSLabeler.csproj|solutionrelative:mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BSLabeler\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BSLabeler\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-24T08:29:27.667Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BSLabeler\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MainWindow.xaml.cs", "ToolTip": "D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BSLabeler\\MainWindow.xaml.cs", "RelativeToolTip": "MainWindow.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-24T08:29:25.905Z"}]}]}]}