<Window x:Class="BSLabeler.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:BSLabeler"
        mc:Ignorable="d"
        Title="肠鸣音标注器" Height="550" Width="750" ResizeMode="CanMinimize"
        WindowStartupLocation="CenterScreen">
    
    <Window.Resources>
        <Style x:Key="TitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,10"/>
        </Style>
        
        <Style x:Key="InfoStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="#7F8C8D"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="10,5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Background" Value="#ECF0F1"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D5DBDB"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#BDC3C7"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#27AE60"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#27AE60"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#229954"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#1E8449"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#95A5A6"/>
                    <Setter Property="BorderBrush" Value="#95A5A6"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="肠鸣音标注器" Style="{StaticResource TitleStyle}"/>
        
        <!-- 说明文字 -->
        <TextBlock Grid.Row="1" Text="选择音频/信号文件进行肠鸣音标注，系统将自动重命名文件" 
                   Style="{StaticResource InfoStyle}"/>
        
        <!-- 默认文件夹设置区域 -->
        <Grid Grid.Row="2" Margin="0,15,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" Name="SetDefaultFolderBtn" Content="设置默认文件夹" 
                    Style="{StaticResource ButtonStyle}" Click="SetDefaultFolderBtn_Click"/>
            <Button Grid.Column="1" Name="ClearDefaultFolderBtn" Content="清除默认文件夹" 
                    Style="{StaticResource ButtonStyle}" Click="ClearDefaultFolderBtn_Click"/>
        </Grid>
        
        <!-- 当前默认文件夹显示 -->
        <TextBlock Grid.Row="3" Name="CurrentFolderLabel" Text="当前默认文件夹: 未设置" 
                   FontSize="10" HorizontalAlignment="Center" Foreground="#7F8C8D" Margin="0,0,0,10"/>
        
        <!-- 文件选择按钮区域 -->
        <Grid Grid.Row="4" Margin="0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" Name="SelectSingleFileBtn" Content="选择单个文件" 
                    Style="{StaticResource ButtonStyle}" Click="SelectSingleFileBtn_Click"/>
            <Button Grid.Column="1" Name="SelectMultipleFilesBtn" Content="选择多个文件" 
                    Style="{StaticResource ButtonStyle}" Click="SelectMultipleFilesBtn_Click"/>
        </Grid>
        
        <!-- 文件列表显示区域 -->
        <Border Grid.Row="5" BorderBrush="#BDC3C7" BorderThickness="1" Margin="0,10" CornerRadius="3">
            <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                <TextBox Name="FileListTextBox" Text="选择文件后将在此显示文件列表..." 
                         IsReadOnly="True" TextWrapping="Wrap" AcceptsReturn="True"
                         Background="White" BorderThickness="0" Padding="10"
                         FontFamily="Consolas" FontSize="11" Foreground="#2C3E50"/>
            </ScrollViewer>
        </Border>
        
        <!-- 开始标注按钮 -->
        <Button Grid.Row="6" Name="StartLabelingBtn" Content="开始标注" 
                Style="{StaticResource PrimaryButtonStyle}" 
                Click="StartLabelingBtn_Click" IsEnabled="False" 
                Height="45" FontSize="14" Margin="0,15,0,10"/>
        
        <!-- 状态栏 -->
        <TextBlock Grid.Row="7" Name="StatusLabel" Text="就绪" 
                   HorizontalAlignment="Center" FontSize="11" Foreground="#7F8C8D"/>
    </Grid>
</Window>
