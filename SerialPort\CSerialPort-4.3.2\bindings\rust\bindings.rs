/* automatically generated by rust-bindgen 0.71.1 */

pub type i_handle_t = ::std::os::raw::c_ulonglong;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct SerialPortInfo {
    pub portName: [::std::os::raw::c_char; 256usize],
    pub description: [::std::os::raw::c_char; 256usize],
    pub hardwareId: [::std::os::raw::c_char; 256usize],
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of SerialPortInfo"][::std::mem::size_of::<SerialPortInfo>() - 768usize];
    ["Alignment of SerialPortInfo"][::std::mem::align_of::<SerialPortInfo>() - 1usize];
    ["Offset of field: SerialPortInfo::portName"]
        [::std::mem::offset_of!(SerialPortInfo, portName) - 0usize];
    ["Offset of field: SerialPortInfo::description"]
        [::std::mem::offset_of!(SerialPortInfo, description) - 256usize];
    ["Offset of field: SerialPortInfo::hardwareId"]
        [::std::mem::offset_of!(SerialPortInfo, hardwareId) - 512usize];
};
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct SerialPortInfoArray {
    pub portInfo: *mut SerialPortInfo,
    pub size: ::std::os::raw::c_uint,
}
#[allow(clippy::unnecessary_operation, clippy::identity_op)]
const _: () = {
    ["Size of SerialPortInfoArray"][::std::mem::size_of::<SerialPortInfoArray>() - 16usize];
    ["Alignment of SerialPortInfoArray"][::std::mem::align_of::<SerialPortInfoArray>() - 8usize];
    ["Offset of field: SerialPortInfoArray::portInfo"]
        [::std::mem::offset_of!(SerialPortInfoArray, portInfo) - 0usize];
    ["Offset of field: SerialPortInfoArray::size"]
        [::std::mem::offset_of!(SerialPortInfoArray, size) - 8usize];
};
pub const OperateMode_AsynchronousOperate: OperateMode = 0;
pub const OperateMode_SynchronousOperate: OperateMode = 1;
pub type OperateMode = ::std::os::raw::c_int;
pub const DataBits_DataBits5: DataBits = 5;
pub const DataBits_DataBits6: DataBits = 6;
pub const DataBits_DataBits7: DataBits = 7;
pub const DataBits_DataBits8: DataBits = 8;
pub type DataBits = ::std::os::raw::c_int;
pub const Parity_ParityNone: Parity = 0;
pub const Parity_ParityOdd: Parity = 1;
pub const Parity_ParityEven: Parity = 2;
pub const Parity_ParityMark: Parity = 3;
pub const Parity_ParitySpace: Parity = 4;
pub type Parity = ::std::os::raw::c_int;
pub const StopBits_StopOne: StopBits = 0;
pub const StopBits_StopOneAndHalf: StopBits = 1;
pub const StopBits_StopTwo: StopBits = 2;
pub type StopBits = ::std::os::raw::c_int;
pub const FlowControl_FlowNone: FlowControl = 0;
pub const FlowControl_FlowHardware: FlowControl = 1;
pub const FlowControl_FlowSoftware: FlowControl = 2;
pub type FlowControl = ::std::os::raw::c_int;
pub type pFunReadEvent = ::std::option::Option<
    unsafe extern "C" fn(
        arg1: i_handle_t,
        arg2: *const ::std::os::raw::c_char,
        arg3: ::std::os::raw::c_uint,
    ),
>;
pub type pFunHotPlugEvent = ::std::option::Option<
    unsafe extern "C" fn(
        arg1: i_handle_t,
        arg2: *const ::std::os::raw::c_char,
        arg3: ::std::os::raw::c_int,
    ),
>;
unsafe extern "C" {
    pub fn CSerialPortAvailablePortInfosMalloc(portInfoArray: *mut SerialPortInfoArray);
}
unsafe extern "C" {
    pub fn CSerialPortAvailablePortInfosFree(portInfoArray: *mut SerialPortInfoArray);
}
unsafe extern "C" {
    pub fn CSerialPortMalloc() -> i_handle_t;
}
unsafe extern "C" {
    pub fn CSerialPortFree(handle: i_handle_t);
}
unsafe extern "C" {
    pub fn CSerialPortInit(
        handle: i_handle_t,
        portName: *const ::std::os::raw::c_char,
        baudRate: ::std::os::raw::c_int,
        parity: Parity,
        dataBits: DataBits,
        stopbits: StopBits,
        flowControl: FlowControl,
        readBufferSize: ::std::os::raw::c_uint,
    );
}
unsafe extern "C" {
    pub fn CSerialPortSetOperateMode(handle: i_handle_t, operateMode: OperateMode);
}
unsafe extern "C" {
    pub fn CSerialPortOpen(handle: i_handle_t) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortClose(handle: i_handle_t);
}
unsafe extern "C" {
    pub fn CSerialPortIsOpen(handle: i_handle_t) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortConnectReadEvent(
        handle: i_handle_t,
        pFun: pFunReadEvent,
    ) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortDisconnectReadEvent(handle: i_handle_t) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortConnectHotPlugEvent(
        handle: i_handle_t,
        pFun: pFunHotPlugEvent,
    ) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortDisconnectHotPlugEvent(handle: i_handle_t) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortGetReadBufferUsedLen(handle: i_handle_t) -> ::std::os::raw::c_uint;
}
unsafe extern "C" {
    pub fn CSerialPortReadData(
        handle: i_handle_t,
        data: *mut ::std::os::raw::c_void,
        size: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortReadAllData(
        handle: i_handle_t,
        data: *mut ::std::os::raw::c_void,
    ) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortReadLineData(
        handle: i_handle_t,
        data: *mut ::std::os::raw::c_void,
        size: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortWriteData(
        handle: i_handle_t,
        data: *const ::std::os::raw::c_void,
        size: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortSetDebugModel(handle: i_handle_t, isDebug: ::std::os::raw::c_int);
}
unsafe extern "C" {
    pub fn CSerialPortSetReadIntervalTimeout(handle: i_handle_t, msecs: ::std::os::raw::c_uint);
}
unsafe extern "C" {
    pub fn CSerialPortGetReadIntervalTimeout(handle: i_handle_t) -> ::std::os::raw::c_uint;
}
unsafe extern "C" {
    pub fn CSerialPortSetMinByteReadNotify(
        handle: i_handle_t,
        minByteReadNotify: ::std::os::raw::c_uint,
    );
}
unsafe extern "C" {
    pub fn CSerialPortFlushBuffers(handle: i_handle_t) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortFlushReadBuffers(handle: i_handle_t) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortFlushWriteBuffers(handle: i_handle_t) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortGetLastError(handle: i_handle_t) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortGetLastErrorMsg(handle: i_handle_t) -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    pub fn CSerialPortClearError(handle: i_handle_t);
}
unsafe extern "C" {
    pub fn CSerialPortSetPortName(handle: i_handle_t, portName: *const ::std::os::raw::c_char);
}
unsafe extern "C" {
    pub fn CSerialPortGetPortName(handle: i_handle_t) -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    pub fn CSerialPortSetBaudRate(handle: i_handle_t, baudRate: ::std::os::raw::c_int);
}
unsafe extern "C" {
    pub fn CSerialPortGetBaudRate(handle: i_handle_t) -> ::std::os::raw::c_int;
}
unsafe extern "C" {
    pub fn CSerialPortSetParity(handle: i_handle_t, parity: Parity);
}
unsafe extern "C" {
    pub fn CSerialPortGetParity(handle: i_handle_t) -> Parity;
}
unsafe extern "C" {
    pub fn CSerialPortSetDataBits(handle: i_handle_t, dataBits: DataBits);
}
unsafe extern "C" {
    pub fn CSerialPortGetDataBits(handle: i_handle_t) -> DataBits;
}
unsafe extern "C" {
    pub fn CSerialPortSetStopBits(handle: i_handle_t, stopbits: StopBits);
}
unsafe extern "C" {
    pub fn CSerialPortGetStopBits(handle: i_handle_t) -> StopBits;
}
unsafe extern "C" {
    pub fn CSerialPortSetFlowControl(handle: i_handle_t, flowControl: FlowControl);
}
unsafe extern "C" {
    pub fn CSerialPortGetFlowControl(handle: i_handle_t) -> FlowControl;
}
unsafe extern "C" {
    pub fn CSerialPortSetReadBufferSize(handle: i_handle_t, size: ::std::os::raw::c_uint);
}
unsafe extern "C" {
    pub fn CSerialPortGetReadBufferSize(handle: i_handle_t) -> ::std::os::raw::c_uint;
}
unsafe extern "C" {
    pub fn CSerialPortSetDtr(handle: i_handle_t, set: ::std::os::raw::c_int);
}
unsafe extern "C" {
    pub fn CSerialPortSetRts(handle: i_handle_t, set: ::std::os::raw::c_int);
}
unsafe extern "C" {
    pub fn CSerialPortGetVersion(handle: i_handle_t) -> *const ::std::os::raw::c_char;
}
