# C# Code Review Checklist

## Code Quality & Style

### Naming Conventions
- [ ] Classes use PascalCase (e.g., `CustomerService`)
- [ ] Methods use PascalCase (e.g., `GetCustomerById`)
- [ ] Properties use PascalCase (e.g., `FirstName`)
- [ ] Local variables use camelCase (e.g., `customerName`)
- [ ] Parameters use camelCase (e.g., `customerId`)
- [ ] Constants use PascalCase (e.g., `MaxRetryCount`)
- [ ] Private fields use camelCase with underscore prefix (e.g., `_customerService`)
- [ ] Interfaces start with 'I' (e.g., `ICustomerService`)
- [ ] Names are meaningful and descriptive
- [ ] Avoid abbreviations and Hungarian notation

### Code Structure
- [ ] Classes follow Single Responsibility Principle
- [ ] Methods are focused and do one thing well
- [ ] Method length is reasonable (typically < 20 lines)
- [ ] Class length is reasonable (typically < 300 lines)
- [ ] Proper separation of concerns
- [ ] Appropriate use of namespaces
- [ ] Logical code organization

### Comments & Documentation
- [ ] XML documentation for public APIs
- [ ] Complex logic is well-commented
- [ ] Comments explain 'why', not 'what'
- [ ] No commented-out code
- [ ] README files are up to date

## Performance & Memory

### Memory Management
- [ ] IDisposable objects are properly disposed
- [ ] Using statements used for disposable resources
- [ ] No obvious memory leaks
- [ ] Appropriate collection types used
- [ ] String concatenation uses StringBuilder for loops
- [ ] Avoid unnecessary object allocations

### Async/Await Patterns
- [ ] Async methods return Task or Task<T>
- [ ] Async methods have 'Async' suffix
- [ ] ConfigureAwait(false) used in library code
- [ ] No async void (except event handlers)
- [ ] Proper cancellation token usage
- [ ] No blocking calls on async code (no .Result or .Wait())

### LINQ & Collections
- [ ] Efficient LINQ queries
- [ ] Appropriate use of deferred execution
- [ ] No unnecessary multiple enumerations
- [ ] Proper use of collection types (List vs Array vs IEnumerable)

## Security

### Input Validation
- [ ] All user inputs are validated
- [ ] SQL injection prevention (parameterized queries)
- [ ] XSS protection in web applications
- [ ] Path traversal prevention
- [ ] Proper data sanitization

### Authentication & Authorization
- [ ] Proper access control implementation
- [ ] Secure credential handling
- [ ] No hardcoded secrets or passwords
- [ ] Appropriate use of HTTPS
- [ ] Proper session management

### Data Protection
- [ ] Sensitive data is encrypted
- [ ] Proper key management
- [ ] Secure data transmission
- [ ] Appropriate logging (no sensitive data in logs)

## Error Handling

### Exception Management
- [ ] Appropriate exception types used
- [ ] Custom exceptions when needed
- [ ] Proper exception handling hierarchy
- [ ] No swallowing exceptions without logging
- [ ] Meaningful error messages
- [ ] Proper cleanup in finally blocks or using statements

### Logging
- [ ] Appropriate logging levels used
- [ ] Structured logging implemented
- [ ] No sensitive information in logs
- [ ] Performance impact of logging considered
- [ ] Proper log correlation IDs

## Design Patterns & Architecture

### SOLID Principles
- [ ] Single Responsibility Principle followed
- [ ] Open/Closed Principle applied
- [ ] Liskov Substitution Principle respected
- [ ] Interface Segregation Principle used
- [ ] Dependency Inversion Principle implemented

### Design Patterns
- [ ] Appropriate design patterns used
- [ ] Dependency Injection properly implemented
- [ ] Repository pattern used for data access
- [ ] Factory patterns where appropriate
- [ ] Observer pattern for events

### Architecture
- [ ] Clear separation of layers
- [ ] Proper abstraction levels
- [ ] Minimal coupling between components
- [ ] High cohesion within components

## Testing

### Unit Tests
- [ ] Adequate test coverage (>80%)
- [ ] Tests follow AAA pattern (Arrange, Act, Assert)
- [ ] Test names are descriptive
- [ ] Tests are independent and isolated
- [ ] Proper use of mocking frameworks
- [ ] Edge cases are tested
- [ ] Negative test cases included

### Integration Tests
- [ ] API endpoints tested
- [ ] Database integration tested
- [ ] External service integration tested
- [ ] Configuration testing

## Configuration & Deployment

### Configuration Management
- [ ] Configuration externalized
- [ ] Environment-specific settings
- [ ] No hardcoded configuration values
- [ ] Proper secrets management
- [ ] Configuration validation

### Dependencies
- [ ] NuGet packages are up to date
- [ ] No vulnerable packages
- [ ] Minimal dependency footprint
- [ ] Proper package versioning
- [ ] License compatibility checked

## Code Metrics

### Complexity
- [ ] Cyclomatic complexity is reasonable
- [ ] Nesting levels are minimal
- [ ] Code duplication is minimized
- [ ] Maintainability index is good

### Performance Metrics
- [ ] No obvious performance bottlenecks
- [ ] Database queries are optimized
- [ ] Caching implemented where appropriate
- [ ] Resource usage is efficient

## Final Review

### Overall Assessment
- [ ] Code meets functional requirements
- [ ] Code is maintainable and readable
- [ ] Code follows team standards
- [ ] Documentation is complete
- [ ] Tests pass and provide good coverage
- [ ] No critical security vulnerabilities
- [ ] Performance is acceptable
- [ ] Code is ready for production deployment

### Action Items
- [ ] High priority issues identified and tracked
- [ ] Medium priority improvements noted
- [ ] Low priority suggestions documented
- [ ] Follow-up review scheduled if needed
