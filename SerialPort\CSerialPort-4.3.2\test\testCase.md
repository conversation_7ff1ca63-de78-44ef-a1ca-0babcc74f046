| No.  | function   | 测试用例                                                     | test case | comment |
| :--: | :--------- | :----------------------------------------------------------- | :-------- | :------ |
| 0_1  | 前置条件   | 是否包含指定虚拟串口                                         |           |         |
| 1_1  | open       | 打开未占用的串口(9600,N,1)                                   |           |         |
| 1_2  | open       | 打开占用的串口(9600,N,1)                                     |           |         |
| 1_3  | open       | 打开两个串口(COM1,9600,N,1)和(COM1,9600,N,1)                 |           |         |
| 2_1  | isOpen     | 打开未占用的串口，验证open()返回值是否与isOpen()相等         |           |         |
| 2_2  | isOpen     | 打开占用的串口，验证open()返回值是否与isOpen()相等           |           |         |
| 3_1  | open,close | 打开串口(9600,N,1),并关闭                                    |           |         |
| 3_2  | open,close | 打开并关闭串口(9600,N,1)，再次打开关闭串口(9600,N,1)         |           |         |
| 3_3  | open,close | 打开并关闭串口(9600,N,1)，切换串口，再次打开串口(9600,N,1)   |           |         |
| 3_4  | open,close | 打开并关闭串口(9600,N,1)，切换波特率，再次打开串口(115200,N,1) |           |         |