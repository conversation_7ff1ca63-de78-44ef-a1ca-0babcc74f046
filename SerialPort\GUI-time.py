#import warnings
#warnings.filterwarnings("ignore", category=DeprecationWarning)

import serial
import pyqtgraph as pg
import threading
import csv
import time
import os
import numpy as np
from collections import deque
from PyQt5 import QtWidgets, QtCore, QtGui

# 全局变量
data_lock = threading.Lock()
N = 2575
data = deque(maxlen=N)
data_dual = deque(maxlen=N)
data_count = 0
csv_file_count = 1
start_time = time.perf_counter()
fs = 2575
n_fft = 512
acquisition_running = False
com = None

acquisition_thread = None

# 信号类，用于在线程间传递数据
class DataSignal(QtCore.QObject):
    data_count_signal = QtCore.pyqtSignal(str, int)

data_signal = DataSignal()

def COM():
    global data_count, csv_file_count, acquisition_running, com

    csv_file = create_csv_file()
    writer = csv.writer(csv_file)
    writer.writerow(['Time', 'Data1', 'Data2'])

    data_points_per_time = {}  # 用于统计每个时间戳的数据点数

    while acquisition_running:
        try:
            data_str = com.readline().decode('ascii').strip()
            if not data_str:
                continue
            data_list = data_str.split()

            if len(data_list) != 2:
                continue

            data1 = int(data_list[0])
            data2 = int(data_list[1])

            current_time = get_current_time()

            with data_lock:
                data.append(data1)
                data_dual.append(data2)

            writer.writerow([current_time, data1, data2])
            csv_file.flush()

            data_count += 1

            # 更新每秒的数据点数
            if current_time in data_points_per_time:
                data_points_per_time[current_time] += 1
            else:
                # 当时间戳变化时，发送上一个时间戳的数据点数
                if len(data_points_per_time) > 0:
                    last_time = list(data_points_per_time.keys())[-1]
                    data_count_for_last_time = data_points_per_time.pop(last_time)
                    # 发射信号，更新GUI显示
                    data_signal.data_count_signal.emit(last_time, data_count_for_last_time)
                data_points_per_time[current_time] = 1

            if data_count == 1000000:
                csv_file.close()
                csv_file_count += 1
                csv_file = create_csv_file()
                writer = csv.writer(csv_file)
                writer.writerow(['Time', 'Data1', 'Data2'])
                data_count = 0
        except Exception as e:
            print(f"Error reading data: {e}")
            continue

    # 处理最后一个时间戳的数据点数
    if len(data_points_per_time) > 0:
        last_time = list(data_points_per_time.keys())[-1]
        data_count_for_last_time = data_points_per_time.pop(last_time)
        data_signal.data_count_signal.emit(last_time, data_count_for_last_time)

    csv_file.close()

def plotData(curve):
    with data_lock:
        if len(data) == N:
            curve.setData(list(data))

def plotDataDual(curve2):
    with data_lock:
        if len(data_dual) == N:
            curve2.setData(list(data_dual))

def plotSpectrum(curve_spectrum):
    with data_lock:
        if len(data) >= n_fft:
            data_array = np.array(data)
            spectrum = np.fft.fft(data_array[-n_fft:])
            freq = np.fft.fftfreq(n_fft, d=1/fs)
            positive_freq_indices = np.where(freq >= 0)
            curve_spectrum.setData(freq[positive_freq_indices], np.abs(spectrum)[positive_freq_indices])

def create_csv_file():
    global csv_file_count
    directory = 'data'
    if not os.path.exists(directory):
        os.makedirs(directory)
    csv_file_name = f'{directory}/data{csv_file_count}.csv'
    return open(csv_file_name, 'w', newline='')

def get_current_time():
    current_time = int((time.perf_counter() - start_time))
    minutes = current_time // 60
    seconds = current_time % 60
    return f"{minutes:02d}:{seconds:02d}"

class MainWindow(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()

        self.initUI()

        self.timer = QtCore.QTimer()
        self.timer.timeout.connect(lambda: plotData(self.curve))
        self.timer_dual = QtCore.QTimer()
        self.timer_dual.timeout.connect(lambda: plotDataDual(self.curve2))
        self.timer_spectrum = QtCore.QTimer()
        self.timer_spectrum.timeout.connect(lambda: plotSpectrum(self.curve_spectrum))

        # 连接信号和槽，更新每秒的数据点数显示
        data_signal.data_count_signal.connect(self.updateDataRateDisplay)

    def initUI(self):
        global N, fs

        self.setWindowTitle('Bowel sounds data acquisition and display')
        self.resize(2000, 1000)

        # 创建按钮
        self.startButton = QtWidgets.QPushButton('Start')
        self.stopButton = QtWidgets.QPushButton('Stop')

        self.startButton.setFixedSize(1000, 30)
        self.stopButton.setFixedSize(1000, 30)

        font = QtGui.QFont()
        font.setFamily('微软雅黑')
        font.setPointSize(14)
        font.setBold(True)

        self.startButton.setFont(font)
        self.stopButton.setFont(font)

        self.startButton.clicked.connect(self.startAcquisition)
        self.stopButton.clicked.connect(self.stopAcquisition)

        # 创建绘图部件
        self.win1 = pg.PlotWidget()
        self.win1.setTitle('<span style="font-size:18pt; font-weight:bold">Body microphone</span>')
        self.win1.showGrid(x=True, y=True)
        self.win1.setRange(xRange=[0, N - 1], yRange=[5000, 10000], padding=0)
        self.curve = self.win1.plot(pen=pg.mkPen(color='y', width=2), name='内部麦克风')

        self.win2 = pg.PlotWidget()
        self.win2.setTitle('<span style="font-size:18pt; font-weight:bold">Ambient Microphone</span>')
        self.win2.showGrid(x=True, y=True)
        self.win2.setRange(xRange=[0, N - 1], yRange=[5000, 10000], padding=0)
        self.curve2 = self.win2.plot(pen=pg.mkPen(color='r', width=2), name='外部麦克风')

        self.win_spectrum = pg.PlotWidget()
        self.win_spectrum.setTitle('<span style="font-size:18pt; font-weight:bold">Spectrogram of the body microphone</span>')
        self.win_spectrum.showGrid(x=True, y=True)
        self.win_spectrum.setRange(xRange=[0, fs / 2], yRange=[0, 30000], padding=0)
        self.curve_spectrum = self.win_spectrum.plot(pen=pg.mkPen(color='g', width=2), name='频谱')

        # 新增：创建一个标签，用于显示每秒接收到的数据点数
        self.dataRateLabel = QtWidgets.QLabel("Data Points in 00:00: 0")
        self.dataRateLabel.setAlignment(QtCore.Qt.AlignCenter)
        self.dataRateLabel.setFont(QtGui.QFont('微软雅黑', 14, QtGui.QFont.Bold))

        # 设置绘图部件的最小尺寸
        self.win1.setMinimumHeight(250)
        self.win2.setMinimumHeight(250)
        self.win_spectrum.setMinimumWidth(400)

        # 设置绘图部件的大小策略
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        self.win1.setSizePolicy(sizePolicy)
        self.win2.setSizePolicy(sizePolicy)
        self.win_spectrum.setSizePolicy(sizePolicy)

        # 布局设置
        mainLayout = QtWidgets.QVBoxLayout()
        buttonLayout = QtWidgets.QHBoxLayout()
        buttonLayout.addWidget(self.startButton)
        buttonLayout.addWidget(self.stopButton)
        mainLayout.addLayout(buttonLayout)

        plotLayout = QtWidgets.QHBoxLayout()

        # 左侧布局：通道1和通道2
        leftLayout = QtWidgets.QVBoxLayout()
        leftLayout.addWidget(self.win1)
        leftLayout.addWidget(self.win2)

        # 设置左侧布局中控件的伸缩因子
        leftLayout.setStretchFactor(self.win1, 1)
        leftLayout.setStretchFactor(self.win2, 1)

        # 右侧布局：频谱图和数据点数显示
        rightLayout = QtWidgets.QVBoxLayout()
        rightLayout.addWidget(self.dataRateLabel)  # 将数据点数显示添加到频谱图上方
        rightLayout.addWidget(self.win_spectrum)

        # 设置右侧布局中控件的伸缩因子
        rightLayout.setStretchFactor(self.dataRateLabel, 0)
        rightLayout.setStretchFactor(self.win_spectrum, 1)

        # 将左右布局添加到主布局中，并设置伸缩因子
        plotLayout.addLayout(leftLayout, stretch=6)  # 左侧布局占6份空间
        plotLayout.addLayout(rightLayout, stretch=2)  # 右侧布局占2份空间

        mainLayout.addLayout(plotLayout)
        self.setLayout(mainLayout)

    def startAcquisition(self):
        global acquisition_running, com, acquisition_thread

        if not acquisition_running:
            try:
                com = serial.Serial('COM3', 460800)
            except Exception as e:
                QtWidgets.QMessageBox.critical(self, "错误", f"无法打开串口: {e}")
                return

            acquisition_running = True
            acquisition_thread = threading.Thread(target=COM)
            acquisition_thread.start()

            # 等待数据数组填满
            while len(data) < N or len(data_dual) < N:
                time.sleep(0.01)

            self.timer.start(10)
            self.timer_dual.start(10)
            self.timer_spectrum.start(100)

    def stopAcquisition(self):
        global acquisition_running, com, acquisition_thread

        if acquisition_running:
            acquisition_running = False
            acquisition_thread.join()
            com.close()

            self.timer.stop()
            self.timer_dual.stop()
            self.timer_spectrum.stop()

    def updateDataRateDisplay(self, time_str, count):
        # 更新标签显示
        self.dataRateLabel.setText(f"Data Points in {time_str}: {count}")

def main():
    app = QtWidgets.QApplication([])
    window = MainWindow()
    window.show()
    app.exec()

if __name__ == '__main__':
    main()
