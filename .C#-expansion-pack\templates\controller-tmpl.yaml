# <!-- Powered by C# Expansion Pack -->
template:
  id: csharp-controller-template
  name: C# Web API Controller
  version: 1.0
  output:
    format: csharp_file
    filename: "{{controller_name}}Controller.cs"
    title: "{{controller_name}} API Controller"

workflow:
  mode: interactive
  elicitation: basic

sections:
  - id: controller_config
    title: Controller Configuration
    fields:
      - name: controller_name
        type: text
        prompt: "What is the name of your controller (without 'Controller' suffix)?"
        validation: "^[A-Za-z][A-Za-z0-9]*$"
      - name: entity_name
        type: text
        prompt: "What entity does this controller manage?"
        default: "{{controller_name}}"
      - name: use_async
        type: boolean
        prompt: "Use async/await patterns?"
        default: true
      - name: include_crud
        type: boolean
        prompt: "Include full CRUD operations?"
        default: true
      - name: use_dto
        type: boolean
        prompt: "Use DTOs for request/response?"
        default: true
      - name: include_validation
        type: boolean
        prompt: "Include input validation?"
        default: true

template_content: |
  using Microsoft.AspNetCore.Mvc;
  {% if use_dto %}using {{project_name}}.Core.DTOs;{% endif %}
  using {{project_name}}.Core.Interfaces;
  using {{project_name}}.Core.Entities;
  {% if include_validation %}using FluentValidation;{% endif %}

  namespace {{project_name}}.Api.Controllers;

  /// <summary>
  /// Controller for managing {{entity_name}} operations
  /// </summary>
  [ApiController]
  [Route("api/[controller]")]
  [Produces("application/json")]
  public class {{controller_name}}Controller : ControllerBase
  {
      private readonly I{{entity_name}}Service _{{entity_name|lower}}Service;
      private readonly ILogger<{{controller_name}}Controller> _logger;
      {% if include_validation %}private readonly IValidator<{{entity_name}}{% if use_dto %}Dto{% endif %}> _validator;{% endif %}

      public {{controller_name}}Controller(
          I{{entity_name}}Service {{entity_name|lower}}Service,
          ILogger<{{controller_name}}Controller> logger{% if include_validation %},
          IValidator<{{entity_name}}{% if use_dto %}Dto{% endif %}> validator{% endif %})
      {
          _{{entity_name|lower}}Service = {{entity_name|lower}}Service;
          _logger = logger;
          {% if include_validation %}_validator = validator;{% endif %}
      }

      {% if include_crud %}
      /// <summary>
      /// Get all {{entity_name|lower}} items
      /// </summary>
      /// <returns>List of {{entity_name|lower}} items</returns>
      [HttpGet]
      [ProducesResponseType(typeof(IEnumerable<{{entity_name}}{% if use_dto %}Dto{% endif %}>), StatusCodes.Status200OK)]
      public {% if use_async %}async Task<ActionResult<IEnumerable<{{entity_name}}{% if use_dto %}Dto{% endif %}>>>{% else %}ActionResult<IEnumerable<{{entity_name}}{% if use_dto %}Dto{% endif %}>>{% endif %} Get{{controller_name}}()
      {
          try
          {
              var items = {% if use_async %}await {% endif %}_{{entity_name|lower}}Service.GetAll{{controller_name}}{% if use_async %}Async{% endif %}();
              return Ok(items);
          }
          catch (Exception ex)
          {
              _logger.LogError(ex, "Error retrieving {{entity_name|lower}} items");
              return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving data");
          }
      }

      /// <summary>
      /// Get a specific {{entity_name|lower}} by ID
      /// </summary>
      /// <param name="id">The {{entity_name|lower}} ID</param>
      /// <returns>The {{entity_name|lower}} item</returns>
      [HttpGet("{id}")]
      [ProducesResponseType(typeof({{entity_name}}{% if use_dto %}Dto{% endif %}), StatusCodes.Status200OK)]
      [ProducesResponseType(StatusCodes.Status404NotFound)]
      public {% if use_async %}async Task<ActionResult<{{entity_name}}{% if use_dto %}Dto{% endif %}>>{% else %}ActionResult<{{entity_name}}{% if use_dto %}Dto{% endif %}>{% endif %} Get{{entity_name}}(int id)
      {
          try
          {
              var item = {% if use_async %}await {% endif %}_{{entity_name|lower}}Service.GetById{% if use_async %}Async{% endif %}(id);
              if (item == null)
              {
                  return NotFound($"{{entity_name}} with ID {id} not found");
              }
              return Ok(item);
          }
          catch (Exception ex)
          {
              _logger.LogError(ex, "Error retrieving {{entity_name|lower}} with ID {Id}", id);
              return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving data");
          }
      }

      /// <summary>
      /// Create a new {{entity_name|lower}}
      /// </summary>
      /// <param name="{{entity_name|lower}}{% if use_dto %}Dto{% endif %}">The {{entity_name|lower}} data</param>
      /// <returns>The created {{entity_name|lower}}</returns>
      [HttpPost]
      [ProducesResponseType(typeof({{entity_name}}{% if use_dto %}Dto{% endif %}), StatusCodes.Status201Created)]
      [ProducesResponseType(StatusCodes.Status400BadRequest)]
      public {% if use_async %}async Task<ActionResult<{{entity_name}}{% if use_dto %}Dto{% endif %}>>{% else %}ActionResult<{{entity_name}}{% if use_dto %}Dto{% endif %}>{% endif %} Create{{entity_name}}([FromBody] {{entity_name}}{% if use_dto %}Dto{% endif %} {{entity_name|lower}}{% if use_dto %}Dto{% endif %})
      {
          {% if include_validation %}
          var validationResult = {% if use_async %}await {% endif %}_validator.Validate{% if use_async %}Async{% endif %}({{entity_name|lower}}{% if use_dto %}Dto{% endif %});
          if (!validationResult.IsValid)
          {
              return BadRequest(validationResult.Errors);
          }
          {% endif %}

          try
          {
              var created{{entity_name}} = {% if use_async %}await {% endif %}_{{entity_name|lower}}Service.Create{% if use_async %}Async{% endif %}({{entity_name|lower}}{% if use_dto %}Dto{% endif %});
              return CreatedAtAction(nameof(Get{{entity_name}}), new { id = created{{entity_name}}.Id }, created{{entity_name}});
          }
          catch (Exception ex)
          {
              _logger.LogError(ex, "Error creating {{entity_name|lower}}");
              return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while creating the {{entity_name|lower}}");
          }
      }

      /// <summary>
      /// Update an existing {{entity_name|lower}}
      /// </summary>
      /// <param name="id">The {{entity_name|lower}} ID</param>
      /// <param name="{{entity_name|lower}}{% if use_dto %}Dto{% endif %}">The updated {{entity_name|lower}} data</param>
      /// <returns>The updated {{entity_name|lower}}</returns>
      [HttpPut("{id}")]
      [ProducesResponseType(typeof({{entity_name}}{% if use_dto %}Dto{% endif %}), StatusCodes.Status200OK)]
      [ProducesResponseType(StatusCodes.Status400BadRequest)]
      [ProducesResponseType(StatusCodes.Status404NotFound)]
      public {% if use_async %}async Task<ActionResult<{{entity_name}}{% if use_dto %}Dto{% endif %}>>{% else %}ActionResult<{{entity_name}}{% if use_dto %}Dto{% endif %}>{% endif %} Update{{entity_name}}(int id, [FromBody] {{entity_name}}{% if use_dto %}Dto{% endif %} {{entity_name|lower}}{% if use_dto %}Dto{% endif %})
      {
          {% if include_validation %}
          var validationResult = {% if use_async %}await {% endif %}_validator.Validate{% if use_async %}Async{% endif %}({{entity_name|lower}}{% if use_dto %}Dto{% endif %});
          if (!validationResult.IsValid)
          {
              return BadRequest(validationResult.Errors);
          }
          {% endif %}

          try
          {
              var updated{{entity_name}} = {% if use_async %}await {% endif %}_{{entity_name|lower}}Service.Update{% if use_async %}Async{% endif %}(id, {{entity_name|lower}}{% if use_dto %}Dto{% endif %});
              if (updated{{entity_name}} == null)
              {
                  return NotFound($"{{entity_name}} with ID {id} not found");
              }
              return Ok(updated{{entity_name}});
          }
          catch (Exception ex)
          {
              _logger.LogError(ex, "Error updating {{entity_name|lower}} with ID {Id}", id);
              return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while updating the {{entity_name|lower}}");
          }
      }

      /// <summary>
      /// Delete a {{entity_name|lower}}
      /// </summary>
      /// <param name="id">The {{entity_name|lower}} ID</param>
      /// <returns>No content if successful</returns>
      [HttpDelete("{id}")]
      [ProducesResponseType(StatusCodes.Status204NoContent)]
      [ProducesResponseType(StatusCodes.Status404NotFound)]
      public {% if use_async %}async Task<ActionResult>{% else %}ActionResult{% endif %} Delete{{entity_name}}(int id)
      {
          try
          {
              var success = {% if use_async %}await {% endif %}_{{entity_name|lower}}Service.Delete{% if use_async %}Async{% endif %}(id);
              if (!success)
              {
                  return NotFound($"{{entity_name}} with ID {id} not found");
              }
              return NoContent();
          }
          catch (Exception ex)
          {
              _logger.LogError(ex, "Error deleting {{entity_name|lower}} with ID {Id}", id);
              return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while deleting the {{entity_name|lower}}");
          }
      }
      {% endif %}
  }
