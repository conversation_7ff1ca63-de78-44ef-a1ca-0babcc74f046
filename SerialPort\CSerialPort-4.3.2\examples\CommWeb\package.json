{"name": "cserialport", "version": "1.0.0", "description": "CSerialPort Node.js Web Example", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/itas109/CSerialPort.git"}, "keywords": ["cserialport", "serial", "serialport", "serialportassistant", "uart", "rs232", "nodeserial", "electronserial"], "author": "itas109", "license": "LGPL-3.0-or-later", "bugs": {"url": "https://github.com/itas109/CSerialPort/issues"}, "homepage": "https://github.com/itas109/CSerialPort", "dependencies": {"express": "^4.19.2"}, "scripts": {"build": "cmake-js rebuild", "start": "node index.js"}}