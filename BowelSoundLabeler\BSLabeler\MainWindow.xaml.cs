using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Microsoft.Win32;
using BSLabeler.Properties;

namespace BSLabeler
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private List<string> selectedFiles = new List<string>();
        
        public MainWindow()
        {
            InitializeComponent();
            InitializeApplication();
        }

        private void InitializeApplication()
        {
            // 初始化默认文件夹显示
            UpdateCurrentFolderDisplay();
        }

        #region 默认文件夹管理

        private string GetDefaultFolder()
        {
            try
            {
                string defaultPath = Settings.Default.DefaultFolder;
                if (!string.IsNullOrEmpty(defaultPath) && Directory.Exists(defaultPath))
                {
                    return defaultPath;
                }
                else
                {
                    // 如果路径不存在或为空，返回用户文档文件夹
                    return Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                }
            }
            catch
            {
                return Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            }
        }

        private void SaveDefaultFolder(string folderPath)
        {
            try
            {
                Settings.Default.DefaultFolder = folderPath;
                Settings.Default.Save();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存默认文件夹失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateCurrentFolderDisplay()
        {
            try
            {
                string defaultPath = Settings.Default.DefaultFolder;
                if (!string.IsNullOrEmpty(defaultPath) && Directory.Exists(defaultPath))
                {
                    // 显示路径，如果太长则截断
                    string displayPath = defaultPath.Length > 60 ? 
                        "..." + defaultPath.Substring(defaultPath.Length - 57) : defaultPath;
                    CurrentFolderLabel.Text = $"当前默认文件夹: {displayPath}";
                    CurrentFolderLabel.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#27AE60"));
                }
                else
                {
                    CurrentFolderLabel.Text = "当前默认文件夹: 未设置";
                    CurrentFolderLabel.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#7F8C8D"));
                }
            }
            catch
            {
                CurrentFolderLabel.Text = "当前默认文件夹: 获取失败";
                CurrentFolderLabel.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E74C3C"));
            }
        }

        #endregion

        #region 事件处理器

        private void SetDefaultFolderBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new System.Windows.Forms.FolderBrowserDialog();
                dialog.Description = "选择默认工作文件夹";
                dialog.SelectedPath = GetDefaultFolder();
                
                if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    SaveDefaultFolder(dialog.SelectedPath);
                    UpdateCurrentFolderDisplay();
                    StatusLabel.Text = "默认文件夹已更新";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置默认文件夹时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearDefaultFolderBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(Settings.Default.DefaultFolder))
                {
                    Settings.Default.DefaultFolder = "";
                    Settings.Default.Save();
                    UpdateCurrentFolderDisplay();
                    StatusLabel.Text = "默认文件夹设置已清除";
                }
                else
                {
                    MessageBox.Show("没有设置默认文件夹", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清除默认文件夹时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SelectSingleFileBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new OpenFileDialog();
                dialog.Title = "选择音频/信号文件";
                dialog.InitialDirectory = GetDefaultFolder();
                dialog.Filter = "音频/信号文件|*.mat;*.wav;*.mp3;*.m4a;*.flac|" +
                               "MATLAB文件 (*.mat)|*.mat|" +
                               "WAV文件 (*.wav)|*.wav|" +
                               "MP3文件 (*.mp3)|*.mp3|" +
                               "M4A文件 (*.m4a)|*.m4a|" +
                               "FLAC文件 (*.flac)|*.flac|" +
                               "所有文件|*.*";
                dialog.Multiselect = false;

                if (dialog.ShowDialog() == true)
                {
                    selectedFiles = new List<string> { dialog.FileName };
                    
                    // 保存用户选择的文件夹作为新的默认路径
                    string selectedFolder = System.IO.Path.GetDirectoryName(dialog.FileName);
                    SaveDefaultFolder(selectedFolder);
                    UpdateCurrentFolderDisplay();
                    
                    UpdateFileList();
                    StartLabelingBtn.IsEnabled = true;
                    StatusLabel.Text = "已选择1个文件";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SelectMultipleFilesBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new OpenFileDialog();
                dialog.Title = "选择音频/信号文件";
                dialog.InitialDirectory = GetDefaultFolder();
                dialog.Filter = "音频/信号文件|*.mat;*.wav;*.mp3;*.m4a;*.flac|" +
                               "MATLAB文件 (*.mat)|*.mat|" +
                               "WAV文件 (*.wav)|*.wav|" +
                               "MP3文件 (*.mp3)|*.mp3|" +
                               "M4A文件 (*.m4a)|*.m4a|" +
                               "FLAC文件 (*.flac)|*.flac|" +
                               "所有文件|*.*";
                dialog.Multiselect = true;

                if (dialog.ShowDialog() == true)
                {
                    selectedFiles = dialog.FileNames.ToList();
                    
                    // 保存用户选择的文件夹作为新的默认路径
                    if (selectedFiles.Count > 0)
                    {
                        string selectedFolder = System.IO.Path.GetDirectoryName(selectedFiles[0]);
                        SaveDefaultFolder(selectedFolder);
                        UpdateCurrentFolderDisplay();
                    }
                    
                    UpdateFileList();
                    StartLabelingBtn.IsEnabled = true;
                    StatusLabel.Text = $"已选择{selectedFiles.Count}个文件";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择文件时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void StartLabelingBtn_Click(object sender, RoutedEventArgs e)
        {
            if (selectedFiles.Count == 0)
            {
                MessageBox.Show("请先选择文件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            await StartLabelingProcess();
        }

        #endregion

        #region 辅助方法

        private void UpdateFileList()
        {
            if (selectedFiles.Count == 0)
            {
                FileListTextBox.Text = "选择文件后将在此显示文件列表...";
            }
            else
            {
                var fileList = new StringBuilder();
                for (int i = 0; i < selectedFiles.Count; i++)
                {
                    string fileName = System.IO.Path.GetFileName(selectedFiles[i]);
                    fileList.AppendLine($"{i + 1}. {fileName}");
                }
                FileListTextBox.Text = fileList.ToString();
            }
        }

        private async Task StartLabelingProcess()
        {
            // 禁用按钮防止重复操作
            SetControlsEnabled(false);

            int processedCount = 0;
            int skippedCount = 0;
            int errorCount = 0;

            try
            {
                for (int i = 0; i < selectedFiles.Count; i++)
                {
                    string currentFile = selectedFiles[i];
                    string fileName = System.IO.Path.GetFileNameWithoutExtension(currentFile);
                    string extension = System.IO.Path.GetExtension(currentFile);
                    string directory = System.IO.Path.GetDirectoryName(currentFile);

                    // 更新状态
                    StatusLabel.Text = $"正在处理文件 {i + 1}/{selectedFiles.Count}: {System.IO.Path.GetFileName(currentFile)}";
                    
                    // 让UI更新
                    await Task.Delay(10);

                    // 询问是否包含肠鸣音
                    var result = MessageBox.Show(
                        $"文件: {System.IO.Path.GetFileName(currentFile)}\n\n此文件是否包含肠鸣音？",
                        "肠鸣音标注",
                        MessageBoxButton.YesNoCancel,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Cancel)
                    {
                        skippedCount++;
                        continue;
                    }

                    try
                    {
                        if (result == MessageBoxResult.No)
                        {
                            // 无肠鸣音，添加_no后缀
                            string newFileName = fileName + "_no" + extension;
                            string newFilePath = System.IO.Path.Combine(directory, newFileName);

                            if (await HandleFileRename(currentFile, newFilePath, newFileName))
                            {
                                processedCount++;
                            }
                            else
                            {
                                skippedCount++;
                            }
                        }
                        else if (result == MessageBoxResult.Yes)
                        {
                            // 有肠鸣音，询问数量
                            var countDialog = new InputDialog("请输入肠鸣音的数量:", "肠鸣音数量", "1");
                            countDialog.Owner = this;
                            if (countDialog.ShowDialog() == true)
                            {
                                string countInput = countDialog.Answer;

                                if (int.TryParse(countInput, out int count) && count >= 0)
                                {
                                    // 添加_yes_N后缀
                                    string newFileName = $"{fileName}_yes_{count}{extension}";
                                    string newFilePath = System.IO.Path.Combine(directory, newFileName);

                                    if (await HandleFileRename(currentFile, newFilePath, newFileName))
                                    {
                                        processedCount++;
                                    }
                                    else
                                    {
                                        skippedCount++;
                                    }
                                }
                                else
                                {
                                    MessageBox.Show("请输入有效的非负整数", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                                    skippedCount++;
                                }
                            }
                            else
                            {
                                skippedCount++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        MessageBox.Show($"处理文件 {System.IO.Path.GetFileName(currentFile)} 时出错: {ex.Message}", 
                                      "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }

                // 显示完成信息
                string resultMsg = $"标注完成！\n\n处理成功: {processedCount} 个文件\n跳过: {skippedCount} 个文件\n错误: {errorCount} 个文件";
                MessageBox.Show(resultMsg, "完成", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"标注过程中出现错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 重新启用按钮
                SetControlsEnabled(true);

                // 清空文件列表
                selectedFiles.Clear();
                UpdateFileList();
                StartLabelingBtn.IsEnabled = false;
                StatusLabel.Text = "标注完成，就绪";
            }
        }

        private async Task<bool> HandleFileRename(string currentFile, string newFilePath, string newFileName)
        {
            // 检查文件是否已存在
            if (File.Exists(newFilePath))
            {
                var choice = MessageBox.Show(
                    $"文件 {newFileName} 已存在，是否覆盖？",
                    "文件已存在",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (choice != MessageBoxResult.Yes)
                {
                    return false;
                }
            }

            // 重命名文件
            await Task.Run(() => File.Move(currentFile, newFilePath));
            return true;
        }

        private void SetControlsEnabled(bool enabled)
        {
            StartLabelingBtn.IsEnabled = enabled && selectedFiles.Count > 0;
            SelectSingleFileBtn.IsEnabled = enabled;
            SelectMultipleFilesBtn.IsEnabled = enabled;
            SetDefaultFolderBtn.IsEnabled = enabled;
            ClearDefaultFolderBtn.IsEnabled = enabled;
        }

        #endregion
    }
}
