<?xml version="1.0" encoding="UTF-8"?>
<project version="1">
    <builddir>cserialport-cppcheck-build-dir</builddir>
    <platform>Unspecified</platform>
    <analyze-all-vs-configs>false</analyze-all-vs-configs>
    <check-headers>true</check-headers>
    <check-unused-templates>false</check-unused-templates>
    <max-ctu-depth>10</max-ctu-depth>
    <includedir>
        <dir name="examples/"/>
        <dir name="include/"/>
        <dir name="src/"/>
    </includedir>
    <paths>
        <dir name="."/>
    </paths>
    <exclude>
        <path name="test/"/>
        <path name="lib/"/>
    </exclude>
</project>
