# <!-- Powered by C# Expansion Pack -->
template:
  id: csharp-webapi-template
  name: C# Web API Project
  version: 1.0
  output:
    format: project_structure
    filename: "{{project_name}}"
    title: "{{project_name}} Web API"

workflow:
  mode: interactive
  elicitation: advanced-elicitation

sections:
  - id: project_setup
    title: Project Configuration
    instruction: |
      Configure the basic Web API project settings and structure.
    sections:
      - id: basic_info
        title: Basic Information
        fields:
          - name: project_name
            type: text
            prompt: "What is the name of your Web API project?"
            validation: "^[A-Za-z][A-Za-z0-9._]*$"
          - name: target_framework
            type: select
            prompt: "Which .NET version should we target?"
            options:
              - net8.0
              - net6.0
            default: net8.0
          - name: use_controllers
            type: boolean
            prompt: "Use traditional controllers (vs minimal APIs)?"
            default: true

  - id: features
    title: Feature Selection
    instruction: |
      Select the features and integrations needed for your Web API.
    sections:
      - id: data_access
        title: Data Access
        fields:
          - name: use_entity_framework
            type: boolean
            prompt: "Include Entity Framework Core?"
            default: true
          - name: database_provider
            type: select
            prompt: "Which database provider?"
            options:
              - SqlServer
              - PostgreSQL
              - SQLite
              - InMemory
            default: SqlServer
            condition: use_entity_framework

      - id: authentication
        title: Authentication & Security
        fields:
          - name: use_authentication
            type: boolean
            prompt: "Include authentication?"
            default: true
          - name: auth_type
            type: select
            prompt: "Authentication type?"
            options:
              - JWT
              - Identity
              - OAuth2
            default: JWT
            condition: use_authentication

      - id: documentation
        title: API Documentation
        fields:
          - name: use_swagger
            type: boolean
            prompt: "Include Swagger/OpenAPI documentation?"
            default: true
          - name: use_xml_comments
            type: boolean
            prompt: "Generate XML documentation comments?"
            default: true

      - id: additional_features
        title: Additional Features
        fields:
          - name: use_cors
            type: boolean
            prompt: "Enable CORS support?"
            default: true
          - name: use_rate_limiting
            type: boolean
            prompt: "Include rate limiting?"
            default: false
          - name: use_caching
            type: boolean
            prompt: "Include response caching?"
            default: true
          - name: use_health_checks
            type: boolean
            prompt: "Include health checks?"
            default: true

project_structure:
  solution_file: "{{project_name}}.sln"
  projects:
    - name: "{{project_name}}.Api"
      type: webapi
      framework: "{{target_framework}}"
      folders:
        - Controllers
        - Models
        - Services
        - Data
        - Middleware
        - Extensions
      files:
        - Program.cs
        - appsettings.json
        - appsettings.Development.json
    
    - name: "{{project_name}}.Core"
      type: classlib
      framework: "{{target_framework}}"
      folders:
        - Entities
        - Interfaces
        - Services
        - DTOs
        - Exceptions
    
    - name: "{{project_name}}.Infrastructure"
      type: classlib
      framework: "{{target_framework}}"
      folders:
        - Data
        - Repositories
        - Services
        - Configurations
    
    - name: "{{project_name}}.Tests"
      type: xunit
      framework: "{{target_framework}}"
      folders:
        - Unit
        - Integration
        - Helpers

packages:
  api_project:
    - Microsoft.AspNetCore.OpenApi
    - Swashbuckle.AspNetCore
    - Microsoft.EntityFrameworkCore.Design
    - Serilog.AspNetCore
    - FluentValidation.AspNetCore
  
  core_project:
    - Microsoft.Extensions.DependencyInjection.Abstractions
    - FluentValidation
  
  infrastructure_project:
    - Microsoft.EntityFrameworkCore
    - Microsoft.EntityFrameworkCore.SqlServer
    - Microsoft.Extensions.Configuration
  
  test_project:
    - Microsoft.NET.Test.Sdk
    - xunit
    - xunit.runner.visualstudio
    - Moq
    - FluentAssertions
    - Microsoft.AspNetCore.Mvc.Testing

configuration_files:
  - name: appsettings.json
    content: |
      {
        "Logging": {
          "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
          }
        },
        "AllowedHosts": "*",
        "ConnectionStrings": {
          "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database={{project_name}}Db;Trusted_Connection=true;MultipleActiveResultSets=true"
        }
      }
  
  - name: appsettings.Development.json
    content: |
      {
        "Logging": {
          "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
          }
        }
      }
