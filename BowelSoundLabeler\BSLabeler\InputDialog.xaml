<Window x:Class="BSLabeler.InputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="输入对话框" Height="200" Width="400" 
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize"
        Background="#F8F9FA">
    
    <Window.Resources>
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Background" Value="#ECF0F1"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D5DBDB"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#BDC3C7"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#27AE60"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#27AE60"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#229954"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#1E8449"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 提示文字 -->
        <TextBlock Grid.Row="0" Name="PromptLabel" Text="请输入:" 
                   FontSize="14" Margin="0,0,0,15" TextWrapping="Wrap"/>
        
        <!-- 输入框 -->
        <TextBox Grid.Row="1" Name="InputTextBox" 
                 FontSize="12" Padding="8" 
                 BorderBrush="#BDC3C7" BorderThickness="1"/>
        
        <!-- 空白区域 -->
        <Grid Grid.Row="2"/>
        
        <!-- 按钮区域 -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Name="OkButton" Content="确定" 
                    Style="{StaticResource PrimaryButtonStyle}"
                    Click="OkButton_Click" IsDefault="True" MinWidth="80"/>
            <Button Name="CancelButton" Content="取消" 
                    Style="{StaticResource ButtonStyle}"
                    Click="CancelButton_Click" IsCancel="True" MinWidth="80"/>
        </StackPanel>
    </Grid>
</Window>
