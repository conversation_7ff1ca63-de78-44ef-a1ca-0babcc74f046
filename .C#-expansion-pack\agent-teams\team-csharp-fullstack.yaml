# <!-- Powered by C# Expansion Pack -->
team:
  id: csharp-fullstack
  name: "C# Full-Stack Development Team"
  description: "Specialized team for C# and .NET full-stack application development"
  version: 1.0

members:
  - agent: csharp-dev
    role: lead_developer
    responsibilities:
      - C# code implementation
      - .NET framework guidance
      - Code review and quality assurance
      - Unit testing implementation
      - Performance optimization
    
  - agent: dotnet-architect
    role: solution_architect
    responsibilities:
      - Solution architecture design
      - Technology stack decisions
      - Integration patterns
      - Security architecture
      - Scalability planning
    
  - agent: dev
    role: full_stack_developer
    responsibilities:
      - Frontend development
      - API integration
      - Database design
      - DevOps support
      - Cross-platform compatibility

  - agent: qa
    role: quality_assurance
    responsibilities:
      - Test strategy development
      - Integration testing
      - Performance testing
      - Security testing
      - Quality gate validation

workflows:
  primary:
    - csharp-webapi-development
    - csharp-desktop-development
    - csharp-microservices-development
  
  secondary:
    - brownfield-csharp-enhancement
    - csharp-library-development

collaboration_patterns:
  - pattern: architecture_review
    participants: [dotnet-architect, csharp-dev, dev]
    trigger: "before major implementation phases"
    process: |
      1. Architect presents solution design
      2. Developers review technical feasibility
      3. Team discusses alternatives and trade-offs
      4. Consensus reached on approach
  
  - pattern: code_review
    participants: [csharp-dev, dev]
    trigger: "before merging code changes"
    process: |
      1. <PERSON><PERSON><PERSON> submits code for review
      2. C# expert reviews for best practices
      3. Full-stack developer reviews for integration
      4. Feedback incorporated and approved
  
  - pattern: quality_gate
    participants: [qa, csharp-dev, dotnet-architect]
    trigger: "at major milestones"
    process: |
      1. QA runs comprehensive test suite
      2. C# developer validates code quality metrics
      3. Architect reviews architectural compliance
      4. Gate passed or issues addressed

communication_protocols:
  - daily_standup:
      frequency: daily
      participants: all
      format: "progress, blockers, next steps"
  
  - architecture_sync:
      frequency: weekly
      participants: [dotnet-architect, csharp-dev]
      format: "technical decisions, patterns, standards"
  
  - retrospective:
      frequency: sprint_end
      participants: all
      format: "what worked, what didn't, improvements"

tools_and_standards:
  development:
    - Visual Studio / VS Code
    - .NET 8.0 / .NET 6.0
    - Entity Framework Core
    - xUnit / NUnit for testing
    - Swagger for API documentation
  
  code_quality:
    - SonarQube for static analysis
    - Code coverage tools
    - StyleCop for code style
    - Security scanning tools
  
  collaboration:
    - Git for version control
    - Azure DevOps / GitHub
    - Slack / Teams for communication
    - Confluence for documentation

success_metrics:
  code_quality:
    - Code coverage > 80%
    - Zero critical security vulnerabilities
    - Maintainability index > 70
    - Technical debt ratio < 5%
  
  delivery:
    - Sprint goals achieved > 90%
    - Defect escape rate < 2%
    - Customer satisfaction > 4.5/5
    - Time to market improvement
  
  team_health:
    - Team velocity consistency
    - Knowledge sharing sessions
    - Skill development progress
    - Team satisfaction scores
