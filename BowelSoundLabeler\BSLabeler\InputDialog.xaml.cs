using System;
using System.Windows;

namespace BSLabeler
{
    /// <summary>
    /// InputDialog.xaml 的交互逻辑
    /// </summary>
    public partial class InputDialog : Window
    {
        public string Answer { get; private set; }

        public InputDialog(string prompt, string title = "输入", string defaultResponse = "")
        {
            InitializeComponent();
            
            this.Title = title;
            PromptLabel.Text = prompt;
            InputTextBox.Text = defaultResponse;
            
            // 注册Loaded事件
            this.Loaded += Window_Loaded;
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            Answer = InputTextBox.Text;
            DialogResult = true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            InputTextBox.Focus();
            InputTextBox.SelectAll();
        }
    }
}
