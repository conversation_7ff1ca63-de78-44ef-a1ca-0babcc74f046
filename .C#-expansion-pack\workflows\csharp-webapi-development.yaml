# <!-- Powered by C# Expansion Pack -->
workflow:
  id: csharp-webapi-development
  name: C# Web API Development
  description: >-
    Complete workflow for developing C# Web API applications from initial setup
    through deployment, including best practices and quality gates.
  type: greenfield
  project_types:
    - webapi
    - microservice
    - rest-api

  sequence:
    - step: project_initialization
      agent: csharp-dev
      action: create new C# Web API project
      dependencies:
        - tasks/create-csharp-project
        - templates/webapi-project-tmpl
      notes: |
        Set up the initial project structure with proper layered architecture:
        - API layer for controllers and middleware
        - Core layer for business logic and entities
        - Infrastructure layer for data access and external services
        - Test projects for comprehensive testing

    - step: architecture_design
      agent: dotnet-architect
      action: design solution architecture
      dependencies:
        - templates/architecture-tmpl
        - data/csharp-best-practices
      notes: |
        Define the overall architecture including:
        - Project structure and dependencies
        - Data access patterns
        - Authentication and authorization strategy
        - API design and versioning approach
        - Error handling and logging strategy

    - step: data_layer_setup
      agent: csharp-dev
      action: implement data access layer
      dependencies:
        - tasks/setup-entity-framework
        - templates/repository-pattern-tmpl
      notes: |
        Set up Entity Framework Core with:
        - DbContext configuration
        - Entity models and relationships
        - Repository pattern implementation
        - Database migrations
        - Connection string configuration

    - step: business_logic_implementation
      agent: csharp-dev
      action: implement core business logic
      dependencies:
        - templates/service-layer-tmpl
        - checklists/csharp-code-review-checklist
      notes: |
        Implement business services with:
        - Service interfaces and implementations
        - Business rule validation
        - Domain models and DTOs
        - Dependency injection configuration

    - step: api_controllers_development
      agent: csharp-dev
      action: create API controllers
      dependencies:
        - templates/controller-tmpl
        - tasks/implement-api-endpoints
      notes: |
        Develop RESTful API controllers with:
        - CRUD operations
        - Input validation
        - Error handling
        - HTTP status code management
        - API documentation with Swagger

    - step: authentication_security
      agent: dotnet-architect
      action: implement authentication and security
      dependencies:
        - tasks/setup-jwt-authentication
        - checklists/security-checklist
      notes: |
        Implement security measures:
        - JWT token authentication
        - Authorization policies
        - CORS configuration
        - Input validation and sanitization
        - Rate limiting

    - step: testing_implementation
      agent: csharp-dev
      action: implement comprehensive testing
      dependencies:
        - templates/unit-test-tmpl
        - templates/integration-test-tmpl
        - checklists/testing-checklist
      notes: |
        Create thorough test coverage:
        - Unit tests for business logic
        - Integration tests for API endpoints
        - Mock external dependencies
        - Test data setup and teardown
        - Code coverage analysis

    - step: logging_monitoring
      agent: csharp-dev
      action: implement logging and monitoring
      dependencies:
        - tasks/setup-structured-logging
        - templates/logging-configuration-tmpl
      notes: |
        Set up comprehensive logging:
        - Structured logging with Serilog
        - Log correlation IDs
        - Performance monitoring
        - Health checks
        - Application insights integration

    - step: configuration_management
      agent: csharp-dev
      action: configure application settings
      dependencies:
        - templates/configuration-tmpl
        - checklists/configuration-checklist
      notes: |
        Implement proper configuration:
        - Environment-specific settings
        - Secrets management
        - Configuration validation
        - Feature flags
        - Connection string management

    - step: api_documentation
      agent: csharp-dev
      action: create API documentation
      dependencies:
        - tasks/generate-swagger-docs
        - templates/api-documentation-tmpl
      notes: |
        Generate comprehensive API documentation:
        - Swagger/OpenAPI specification
        - XML documentation comments
        - Example requests and responses
        - Authentication documentation
        - Postman collection

    - step: performance_optimization
      agent: dotnet-architect
      action: optimize performance
      dependencies:
        - checklists/performance-checklist
        - tasks/performance-profiling
      notes: |
        Optimize application performance:
        - Database query optimization
        - Caching implementation
        - Async/await patterns
        - Memory usage optimization
        - Response compression

    - step: code_quality_review
      agent: csharp-dev
      action: conduct code review
      dependencies:
        - tasks/csharp-code-review
        - checklists/csharp-code-review-checklist
      notes: |
        Perform comprehensive code review:
        - Code style and conventions
        - SOLID principles adherence
        - Security vulnerability scan
        - Performance analysis
        - Test coverage verification

    - step: deployment_preparation
      agent: dotnet-architect
      action: prepare for deployment
      dependencies:
        - tasks/setup-ci-cd-pipeline
        - templates/dockerfile-tmpl
      notes: |
        Prepare application for deployment:
        - Docker containerization
        - CI/CD pipeline configuration
        - Environment configuration
        - Database migration scripts
        - Deployment documentation

    - step: final_validation
      agent: csharp-dev
      action: final testing and validation
      dependencies:
        - checklists/deployment-readiness-checklist
      notes: |
        Final validation before deployment:
        - End-to-end testing
        - Load testing
        - Security testing
        - Documentation review
        - Deployment dry run

quality_gates:
  - gate: code_quality
    criteria:
      - code_coverage: ">= 80%"
      - static_analysis: "no critical issues"
      - security_scan: "no high/critical vulnerabilities"
    
  - gate: performance
    criteria:
      - response_time: "< 200ms for 95th percentile"
      - memory_usage: "< 500MB under normal load"
      - database_queries: "optimized and indexed"
    
  - gate: security
    criteria:
      - authentication: "properly implemented"
      - authorization: "role-based access control"
      - input_validation: "all inputs validated"
      - secrets_management: "no hardcoded secrets"

deliverables:
  - name: "Web API Application"
    description: "Fully functional C# Web API with all features implemented"
  - name: "Test Suite"
    description: "Comprehensive unit and integration tests"
  - name: "API Documentation"
    description: "Complete Swagger/OpenAPI documentation"
  - name: "Deployment Package"
    description: "Docker container and deployment scripts"
  - name: "Technical Documentation"
    description: "Architecture and development documentation"
