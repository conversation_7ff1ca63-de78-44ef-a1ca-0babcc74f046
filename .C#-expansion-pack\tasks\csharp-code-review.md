# C# Code Review Task

## Task Definition
- **ID**: csharp-code-review
- **Name**: C# Code Review
- **Description**: Comprehensive review of C# code for best practices, performance, security, and maintainability
- **Agent**: csharp-dev
- **Elicit**: true

## Prerequisites
- C# code files to review
- Project context and requirements
- Coding standards document (if available)

## Elicitation Questions
1. **Scope**: What code should be reviewed?
   - Specific files/classes
   - Entire project
   - Recent changes/commits
   - Specific functionality

2. **Focus Areas**: What aspects should be prioritized?
   - Code quality and maintainability
   - Performance optimization
   - Security vulnerabilities
   - Design patterns and architecture
   - Testing coverage
   - Documentation

3. **Standards**: Are there specific coding standards to follow?
   - Microsoft C# coding conventions
   - Company-specific guidelines
   - Industry standards (e.g., SOLID principles)

## Review Checklist

### Code Quality
- [ ] **Naming Conventions**
  - PascalCase for classes, methods, properties
  - camelCase for local variables, parameters
  - Meaningful and descriptive names
  - No Hungarian notation

- [ ] **Code Structure**
  - Single Responsibility Principle
  - Appropriate class and method sizes
  - Proper separation of concerns
  - Clear code organization

- [ ] **Error Handling**
  - Appropriate exception handling
  - Custom exceptions where needed
  - Proper logging of errors
  - Graceful degradation

### Performance
- [ ] **Memory Management**
  - Proper disposal of resources
  - Using statements for IDisposable
  - Avoiding memory leaks
  - Efficient collection usage

- [ ] **Async/Await**
  - Proper async/await usage
  - ConfigureAwait(false) where appropriate
  - Avoiding async void (except event handlers)
  - Task-based operations

- [ ] **LINQ and Collections**
  - Efficient LINQ queries
  - Appropriate collection types
  - Avoiding unnecessary iterations
  - Lazy evaluation where beneficial

### Security
- [ ] **Input Validation**
  - Proper parameter validation
  - SQL injection prevention
  - XSS protection
  - Data sanitization

- [ ] **Authentication/Authorization**
  - Proper access control
  - Secure credential handling
  - Token management
  - Role-based security

### Design Patterns
- [ ] **SOLID Principles**
  - Single Responsibility
  - Open/Closed
  - Liskov Substitution
  - Interface Segregation
  - Dependency Inversion

- [ ] **Design Patterns**
  - Appropriate pattern usage
  - Factory patterns
  - Repository pattern
  - Dependency injection

### Testing
- [ ] **Unit Tests**
  - Adequate test coverage
  - Test naming conventions
  - Arrange-Act-Assert pattern
  - Mock usage

- [ ] **Integration Tests**
  - API endpoint testing
  - Database integration tests
  - External service mocking

## Review Process

### Step 1: Initial Analysis
- Review project structure
- Understand business requirements
- Identify critical components

### Step 2: Code Examination
- Static code analysis
- Manual code review
- Performance profiling (if needed)
- Security scanning

### Step 3: Documentation Review
- Code comments and documentation
- API documentation
- README and setup instructions

### Step 4: Testing Assessment
- Test coverage analysis
- Test quality evaluation
- Missing test scenarios

## Output Format

### Summary Report
- Overall code quality score
- Key findings and recommendations
- Priority issues to address
- Positive aspects to highlight

### Detailed Findings
For each issue found:
- **File/Location**: Specific location
- **Issue Type**: Category (performance, security, etc.)
- **Severity**: Critical/High/Medium/Low
- **Description**: What the issue is
- **Recommendation**: How to fix it
- **Example**: Code example if helpful

### Action Items
- [ ] High priority fixes
- [ ] Medium priority improvements
- [ ] Low priority suggestions
- [ ] Documentation updates needed
- [ ] Additional testing required

## Follow-up Actions
- Schedule follow-up review
- Create tracking issues
- Update coding standards
- Plan refactoring sessions
