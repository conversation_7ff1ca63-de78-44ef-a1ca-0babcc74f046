<?xml version="1.0" encoding="utf-8" ?>
<wxsmith>
	<object class="wxDialog" name="CommWXWidgetsDialog">
		<title>CommWXWidgets</title>
		<id_arg>0</id_arg>
		<style>wxDEFAULT_DIALOG_STYLE|wxRESIZE_BORDER|wxMAXIMIZE_BOX|wxMINIMIZE_BOX</style>
		<object class="wxBoxSizer" variable="BoxSizer1" member="yes">
			<object class="sizeritem">
				<object class="wxBoxSizer" variable="BoxSizer2" member="no">
					<orient>wxVERTICAL</orient>
					<object class="sizeritem">
						<object class="wxStaticBoxSizer" variable="StaticBoxSizerSetting" member="no">
							<label>SerialPort Setting</label>
							<orient>wxVERTICAL</orient>
							<object class="sizeritem">
								<object class="wxBoxSizer" variable="BoxSizerPortName" member="no">
									<object class="sizeritem">
										<object class="wxStaticText" name="ID_STATICTEXT_PortName" variable="StaticTextPortName" member="yes">
											<label>PortName</label>
											<size>94,21</size>
										</object>
										<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
										<border>10</border>
										<option>1</option>
									</object>
									<object class="sizeritem">
										<object class="wxChoice" name="ID_CHOICE_PortName" variable="ChoicePortName" member="yes" />
										<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
										<border>5</border>
										<option>1</option>
									</object>
								</object>
								<flag>wxALL|wxEXPAND</flag>
								<border>5</border>
								<option>1</option>
							</object>
							<object class="sizeritem">
								<object class="wxBoxSizer" variable="BoxSizer5" member="no">
									<object class="sizeritem">
										<object class="wxStaticText" name="ID_STATICTEXT_Baudrate" variable="StaticTextBaudrate" member="yes">
											<label>Baudrate</label>
										</object>
										<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
										<border>5</border>
										<option>1</option>
									</object>
									<object class="sizeritem">
										<object class="wxChoice" name="ID_CHOICE_Baudrate" variable="ChoiceBaudrate" member="yes">
											<content>
												<item>300</item>
												<item>600</item>
												<item>1200</item>
												<item>2400</item>
												<item>4800</item>
												<item>9600</item>
												<item>14400</item>
												<item>19200</item>
												<item>38400</item>
												<item>56000</item>
												<item>57600</item>
												<item>115200</item>
											</content>
											<selection>5</selection>
										</object>
										<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
										<border>5</border>
										<option>1</option>
									</object>
								</object>
								<flag>wxALL|wxEXPAND</flag>
								<border>5</border>
								<option>1</option>
							</object>
							<object class="sizeritem">
								<object class="wxBoxSizer" variable="BoxSizer6" member="no">
									<object class="sizeritem">
										<object class="wxStaticText" name="ID_STATICTEXT_Parity" variable="StaticTextParity" member="yes">
											<label>Parity</label>
										</object>
										<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
										<border>5</border>
										<option>1</option>
									</object>
									<object class="sizeritem">
										<object class="wxChoice" name="ID_CHOICE_Parity" variable="ChoiceParity" member="yes">
											<content>
												<item>None</item>
												<item>Odd</item>
												<item>Even</item>
												<item>Mark</item>
												<item>Space</item>
											</content>
											<selection>0</selection>
										</object>
										<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
										<border>5</border>
										<option>1</option>
									</object>
								</object>
								<flag>wxALL|wxEXPAND</flag>
								<border>5</border>
								<option>1</option>
							</object>
							<object class="sizeritem">
								<object class="wxBoxSizer" variable="BoxSizer7" member="no">
									<object class="sizeritem">
										<object class="wxStaticText" name="ID_STATICTEXT_DataBit" variable="StaticTextDataBit" member="yes">
											<label>DataBit</label>
										</object>
										<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
										<border>5</border>
										<option>1</option>
									</object>
									<object class="sizeritem">
										<object class="wxChoice" name="ID_CHOICE_DataBit" variable="ChoiceDataBit" member="yes">
											<content>
												<item>5</item>
												<item>6</item>
												<item>7</item>
												<item>8</item>
											</content>
											<selection>3</selection>
										</object>
										<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
										<border>5</border>
										<option>1</option>
									</object>
								</object>
								<flag>wxALL|wxEXPAND</flag>
								<border>5</border>
								<option>1</option>
							</object>
							<object class="sizeritem">
								<object class="wxBoxSizer" variable="BoxSizer8" member="no">
									<object class="sizeritem">
										<object class="wxStaticText" name="ID_STATICTEXT_StopBit" variable="StaticTextStopBit" member="yes">
											<label>StopBit</label>
										</object>
										<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
										<border>5</border>
										<option>1</option>
									</object>
									<object class="sizeritem">
										<object class="wxChoice" name="ID_CHOICE_StopBit" variable="ChoiceStopBit" member="yes">
											<content>
												<item>1</item>
												<item>1.5</item>
												<item>2</item>
											</content>
											<selection>0</selection>
										</object>
										<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
										<border>5</border>
										<option>1</option>
									</object>
								</object>
								<flag>wxALL|wxEXPAND</flag>
								<border>5</border>
								<option>1</option>
							</object>
							<object class="sizeritem">
								<object class="wxButton" name="ID_BUTTON_OpenClose" variable="ButtonOpenClose" member="yes">
									<label>open</label>
									<size>284,41</size>
									<handler function="OnButtonOpenCloseClick" entry="EVT_BUTTON" />
								</object>
								<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
								<border>5</border>
								<option>1</option>
							</object>
						</object>
						<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
						<border>5</border>
					</object>
					<object class="sizeritem">
						<object class="wxBoxSizer" variable="BoxSizer4" member="no">
							<orient>wxVERTICAL</orient>
							<object class="sizeritem">
								<object class="wxStaticText" name="ID_STATICTEXT_Info" variable="StaticTextInfo" member="yes">
									<label>Author: itas109&#x0A;https://github.com/itas109/CSerialPort&#x0A;https://blog.csdn.net/itas109</label>
									<size>299,66</size>
								</object>
								<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
								<border>5</border>
								<option>1</option>
							</object>
							<object class="sizeritem">
								<object class="wxButton" name="ID_BUTTON_Send" variable="ButtonSend" member="yes">
									<label>Send</label>
									<size>293,80</size>
									<handler function="OnButtonSendClick" entry="EVT_BUTTON" />
								</object>
								<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
								<border>5</border>
								<option>1</option>
							</object>
						</object>
						<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
						<border>5</border>
					</object>
				</object>
				<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
				<border>5</border>
			</object>
			<object class="sizeritem">
				<object class="wxBoxSizer" variable="BoxSizer3" member="no">
					<orient>wxVERTICAL</orient>
					<object class="sizeritem">
						<object class="wxStaticBoxSizer" variable="StaticBoxSizerReceive" member="no">
							<label>Receive</label>
							<orient>wxVERTICAL</orient>
							<object class="sizeritem">
								<object class="wxRichTextCtrl" name="ID_RICHTEXTCTRL_Receive" variable="RichTextCtrlReceive" member="yes">
									<size>348,271</size>
								</object>
								<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
								<border>5</border>
								<option>1</option>
							</object>
						</object>
						<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
						<border>5</border>
					</object>
					<object class="sizeritem">
						<object class="wxStaticBoxSizer" variable="StaticBoxSizerSend" member="no">
							<label>Send</label>
							<object class="sizeritem">
								<object class="wxRichTextCtrl" name="ID_RICHTEXTCTRL_Send" variable="RichTextCtrlSend" member="yes">
									<value>https://blog.csdn.net/itas109</value>
									<size>337,120</size>
								</object>
								<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
								<border>5</border>
								<option>1</option>
							</object>
						</object>
						<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
						<border>5</border>
					</object>
					<object class="sizeritem">
						<object class="wxBoxSizer" variable="BoxSizer9" member="no">
							<object class="sizeritem">
								<object class="wxStaticText" name="ID_STATICTEXT_RX" variable="StaticTextRX" member="yes">
									<label>RX:</label>
								</object>
								<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
								<border>5</border>
								<option>1</option>
							</object>
							<object class="sizeritem">
								<object class="wxStaticText" name="ID_STATICTEXT_RXValue" variable="StaticTextRXValue" member="yes">
									<label>0</label>
								</object>
								<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
								<border>5</border>
								<option>1</option>
							</object>
							<object class="sizeritem">
								<object class="wxStaticText" name="ID_STATICTEXT_TX" variable="StaticTextTX" member="yes">
									<label>TX: </label>
								</object>
								<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
								<border>5</border>
								<option>1</option>
							</object>
							<object class="sizeritem">
								<object class="wxStaticText" name="ID_STATICTEXT_TXValue" variable="StaticTextTXValue" member="yes">
									<label>0</label>
								</object>
								<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
								<border>5</border>
								<option>1</option>
							</object>
							<object class="sizeritem">
								<object class="wxButton" name="ID_BUTTON_ClearCount" variable="ButtonClearCount" member="yes">
									<label>CL</label>
									<minsize>50,-1</minsize>
									<maxsize>50,-1</maxsize>
									<handler function="OnButtonClearCountClick" entry="EVT_BUTTON" />
								</object>
								<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
								<border>5</border>
								<option>1</option>
							</object>
						</object>
						<flag>wxALL|wxEXPAND</flag>
						<border>5</border>
					</object>
				</object>
				<flag>wxALL|wxALIGN_CENTER_HORIZONTAL|wxALIGN_CENTER_VERTICAL</flag>
				<border>5</border>
				<option>1</option>
			</object>
		</object>
	</object>
</wxsmith>
