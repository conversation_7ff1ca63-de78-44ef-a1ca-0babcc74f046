<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>702</width>
    <height>563</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <layout class="QVBoxLayout" name="verticalLayout_5" stretch="2">
      <property name="spacing">
       <number>3</number>
      </property>
      <item>
       <widget class="QGroupBox" name="groupBox_2">
        <property name="title">
         <string/>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_4" stretch="5,0,0,0,0">
         <property name="spacing">
          <number>2</number>
         </property>
         <property name="leftMargin">
          <number>2</number>
         </property>
         <property name="topMargin">
          <number>2</number>
         </property>
         <property name="rightMargin">
          <number>2</number>
         </property>
         <property name="bottomMargin">
          <number>2</number>
         </property>
         <item>
          <widget class="QGroupBox" name="groupBox">
           <property name="title">
            <string>SerialPort Setting</string>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_2" stretch="8,1,1,2">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout">
                <item>
                 <widget class="QLabel" name="labelPortName">
                  <property name="text">
                   <string>PortName</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_2">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QComboBox" name="comboBoxPortName">
                  <property name="minimumSize">
                   <size>
                    <width>70</width>
                    <height>0</height>
                   </size>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <spacer name="verticalSpacer">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_2">
                <item>
                 <widget class="QLabel" name="labelBaudrate">
                  <property name="text">
                   <string>Baudrate</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QComboBox" name="comboBoxBaudrate">
                  <property name="minimumSize">
                   <size>
                    <width>70</width>
                    <height>0</height>
                   </size>
                  </property>
                  <item>
                   <property name="text">
                    <string>300</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>600</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>1200</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>2400</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>4800</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>9600</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>14400</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>19200</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>38400</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>56000</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>57600</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>115200</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <spacer name="verticalSpacer_5">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_3">
                <item>
                 <widget class="QLabel" name="labelParity">
                  <property name="text">
                   <string>Parity</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_3">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QComboBox" name="comboBoxParity">
                  <property name="minimumSize">
                   <size>
                    <width>70</width>
                    <height>0</height>
                   </size>
                  </property>
                  <item>
                   <property name="text">
                    <string>None</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Odd</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Even</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Mark</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Space</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <spacer name="verticalSpacer_2">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_4">
                <item>
                 <widget class="QLabel" name="labelDataBit">
                  <property name="text">
                   <string>DataBit</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_4">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QComboBox" name="comboBoxDataBit">
                  <property name="minimumSize">
                   <size>
                    <width>70</width>
                    <height>0</height>
                   </size>
                  </property>
                  <item>
                   <property name="text">
                    <string>5</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>6</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>7</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>8</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <spacer name="verticalSpacer_3">
                <property name="orientation">
                 <enum>Qt::Vertical</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>20</width>
                  <height>40</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_5">
                <item>
                 <widget class="QLabel" name="labelStopBit">
                  <property name="text">
                   <string>StopBit</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_5">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QComboBox" name="comboBoxStopBit">
                  <property name="minimumSize">
                   <size>
                    <width>70</width>
                    <height>0</height>
                   </size>
                  </property>
                  <item>
                   <property name="text">
                    <string>1</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>1.5</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>2</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="verticalSpacer_4">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QCheckBox" name="checkBoxSync">
              <property name="text">
               <string>SynchronousOperate</string>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_10">
              <property name="spacing">
               <number>0</number>
              </property>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_9">
                <property name="spacing">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QCheckBox" name="checkBoxDTR">
                  <property name="text">
                   <string>DTR</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QCheckBox" name="checkBoxRTS">
                  <property name="text">
                   <string>RTS</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <widget class="QPushButton" name="pushButtonOpen">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="text">
                 <string>open</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <item>
            <widget class="QLabel" name="label">
             <property name="text">
              <string>Author:itas109</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_2">
             <property name="text">
              <string>http://github.com/itas109</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_3">
             <property name="text">
              <string>http://blog.csdn.net/itas109</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="verticalSpacer_6">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonSend">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>50</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>50</height>
            </size>
           </property>
           <property name="text">
            <string>Send</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonReadSync">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>50</height>
            </size>
           </property>
           <property name="text">
            <string>Read Sync</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
    <item row="0" column="1">
     <layout class="QVBoxLayout" name="verticalLayout_8" stretch="0,5,5,1">
      <property name="spacing">
       <number>3</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_11" stretch="0,0,0">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="labelReadIntervalTimeoutMS">
          <property name="text">
           <string>ReadIntervalTimeout(ms):</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_8">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEditReadIntervalTimeoutMS">
          <property name="text">
           <string>0</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox_3">
        <property name="title">
         <string>Receive</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_6">
         <item>
          <widget class="QPlainTextEdit" name="plainTextEditReceive"/>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox_4">
        <property name="title">
         <string>Send</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_7">
         <item>
          <widget class="QPlainTextEdit" name="plainTextEditSend">
           <property name="plainText">
            <string>https://blog.csdn.net/itas109</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_8">
        <property name="spacing">
         <number>1</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_7">
          <item>
           <widget class="QLabel" name="labelRX">
            <property name="text">
             <string>RX：</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_6">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="labelRXValue">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_6">
          <item>
           <widget class="QLabel" name="labelTX">
            <property name="text">
             <string>TX:</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_7">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="labelTXValue">
            <property name="text">
             <string>0</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QPushButton" name="pushButtonClear">
          <property name="minimumSize">
           <size>
            <width>25</width>
            <height>25</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
          <property name="text">
           <string>CL</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menuBar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>702</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QToolBar" name="mainToolBar">
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
  </widget>
  <widget class="QStatusBar" name="statusBar"/>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
