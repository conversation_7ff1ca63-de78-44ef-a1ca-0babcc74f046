# =============================================================================
# Git忽略文件 - 多语言项目（C#, MATLAB, Python, C++）
# 生成日期: 2025-08-28
# =============================================================================

# =============================================================================
# C# / .NET 项目忽略规则
# =============================================================================

# 编译输出目录
bin/
obj/
out/

# 编译生成的文件
*.exe
*.dll
*.pdb
*.cache
*.tmp
*.log

# Visual Studio 相关文件
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates
*.userprefs

# MSBuild 相关
project.lock.json
project.fragment.lock.json
artifacts/

# NuGet 包管理
packages/
*.nupkg
*.snupkg
.nuget/
nuget.exe

# 测试结果
TestResults/
[Tt]est[Rr]esult*/
*.coverage
*.coveragexml

# ASP.NET Core
wwwroot/dist/

# Entity Framework
*.edmx.diagram
*.edmx.sql

# =============================================================================
# MATLAB 项目忽略规则
# =============================================================================

# MATLAB 自动生成文件
*.asv
*.autosave
*.m~

# MATLAB 编译文件
*.p
*.mex*
*.fig

# MATLAB 缓存和临时文件
slprj/
*.slxc
codegen/
*.mlapp.bak

# MATLAB 项目文件（可选择性忽略）
*.prj

# =============================================================================
# Python 项目忽略规则
# =============================================================================

# Python 字节码
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# =============================================================================
# C++ 项目忽略规则
# =============================================================================

# 编译输出
*.o
*.obj
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# 调试文件
*.dSYM/
*.su
*.idb
*.pdb

# CMake 相关
CMakeCache.txt
CMakeFiles/
CMakeScripts/
Testing/
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps/

# 构建目录
build/
Build/
BUILD/

# =============================================================================
# IDE 和编辑器忽略规则
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# =============================================================================
# 操作系统生成的文件
# =============================================================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# 日志和临时文件
# =============================================================================

# 日志文件
*.log
logs/
log/

# 临时文件
*.tmp
*.temp
*.bak
*.backup
*.old
*.orig

# 压缩文件（通常不需要版本控制）
*.zip
*.rar
*.7z
*.tar
*.gz
*.bz2
*.xz

# =============================================================================
# 数据文件和输出文件（根据项目需要调整）
# =============================================================================

# 数据文件
*.csv
*.xlsx
*.xls
*.mat
*.dat

# 媒体文件
*.wav
*.mp3
*.mp4
*.avi
*.mov

# 图像文件（如果是生成的输出）
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff

# =============================================================================
# 项目特定忽略规则
# =============================================================================

# 如果有特定的输出目录或配置文件，在这里添加
