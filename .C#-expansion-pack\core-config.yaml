markdownExploder: true
qa:
  qaLocation: docs/qa
prd:
  prdFile: docs/prd.md
  prdVersion: v4
  prdSharded: true
  prdShardedLocation: docs/prd
  epicFilePattern: epic-{n}*.md
architecture:
  architectureFile: docs/architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture
customTechnicalDocuments: null
devLoadAlwaysFiles:
  - docs/architecture/coding-standards.md
  - docs/architecture/tech-stack.md
  - docs/architecture/source-tree.md
  - docs/architecture/csharp-conventions.md
  - docs/architecture/dotnet-patterns.md
devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
slashPrefix: CSharp
expansionPack:
  name: "C# Development Pack"
  version: "1.0.0"
  description: "C# and .NET development focused expansion pack"
  targetFrameworks:
    - net8.0
    - net6.0
    - netstandard2.1
  supportedProjectTypes:
    - console
    - webapi
    - mvc
    - blazor
    - wpf
    - winforms
    - classlib
    - xunit
    - nunit
