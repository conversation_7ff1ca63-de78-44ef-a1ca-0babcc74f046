version: 1.0.0
installed_at: '2025-08-28T12:25:00.000Z'
install_type: expansion_pack
agent: null
ides_setup:
  - cursor
expansion_packs: []
base_pack: bmad-core
files:
  - path: .C#-expansion-pack\core-config.yaml
    hash: auto-generated
    modified: false
  - path: .C#-expansion-pack\install-manifest.yaml
    hash: auto-generated
    modified: false
  - path: .C#-expansion-pack\agents\csharp-dev.md
    hash: auto-generated
    modified: false
  - path: .C#-expansion-pack\agents\dotnet-architect.md
    hash: auto-generated
    modified: false
  - path: .C#-expansion-pack\tasks\create-csharp-project.md
    hash: auto-generated
    modified: false
  - path: .C#-expansion-pack\tasks\manage-nuget-packages.md
    hash: auto-generated
    modified: false
  - path: .C#-expansion-pack\templates\webapi-project-tmpl.yaml
    hash: auto-generated
    modified: false
  - path: .C#-expansion-pack\templates\controller-tmpl.yaml
    hash: auto-generated
    modified: false
  - path: .C#-expansion-pack\checklists\csharp-code-review-checklist.md
    hash: auto-generated
    modified: false
  - path: .C#-expansion-pack\data\csharp-best-practices.md
    hash: auto-generated
    modified: false
  - path: .C#-expansion-pack\workflows\csharp-webapi-development.yaml
    hash: auto-generated
    modified: false
  - path: .C#-expansion-pack\agent-teams\team-csharp-fullstack.yaml
    hash: auto-generated
    modified: false
  - path: .cursor\rules\csharp\csharp-dev.mdc
    hash: auto-generated
    modified: false
  - path: .cursor\rules\csharp\dotnet-architect.mdc
    hash: auto-generated
    modified: false
