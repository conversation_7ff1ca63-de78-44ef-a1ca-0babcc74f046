@echo off
echo 正在编译肠鸣音标注器项目...
echo.

REM 尝试VS2022 Community版本
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo 使用Visual Studio 2022 Community MSBuild...
    "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BSLabeler.sln /p:Configuration=Release /p:Platform="Any CPU" /verbosity:minimal
    goto end
)

REM 尝试VS2022 Professional版本
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo 使用Visual Studio 2022 Professional MSBuild...
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" BSLabeler.sln /p:Configuration=Release /p:Platform="Any CPU" /verbosity:minimal
    goto end
)

REM 尝试VS2022 Enterprise版本
if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    echo 使用Visual Studio 2022 Enterprise MSBuild...
    "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" BSLabeler.sln /p:Configuration=Release /p:Platform="Any CPU" /verbosity:minimal
    goto end
)

echo 错误: 找不到MSBuild工具
echo 请确保安装了Visual Studio 2022，或者直接在Visual Studio中打开项目进行编译
echo.
pause

:end
echo.
if %ERRORLEVEL% EQU 0 (
    echo 编译成功! 可执行文件在 bin\Release\ 文件夹中
    echo 双击 bin\Release\BSLabeler.exe 运行程序
) else (
    echo 编译失败，请检查错误信息
)
echo.
pause

