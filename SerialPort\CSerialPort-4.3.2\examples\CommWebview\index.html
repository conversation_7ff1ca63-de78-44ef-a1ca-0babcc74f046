<!DOCTYPE html>
<html lang='en'>

<head>
    <meta charset='utf-8'>
    <title>CSerialPort Webview Example</title>

    <style>
        #header {
            text-align: center;
            padding: 1px;
        }

        #left {
            position: absolute;
            width: 55%;
            height: 100%;
        }

        #right {
            margin-left: 55%;
            height: 100%;
        }

        #receive {
            width: 50vw;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        #log {
            width: 40vw;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>

<body>
    <div id='header'>CSerialPort Webview Example</div>

    <hr />

    <div>
        <label>PortName</label>
        <select id='listPortSelect'></select>
        <label>BaudRate</label>
        <select id='baudRateSelect'></select>
        <button id='onOpen' onclick='onOpen()'>open</button>
        <button id='onClose' onclick='onClose()'>close</button>
        <input type='checkbox' id='setDtr' onclick='onSetDtr(this)' />
        <label>setDtr</label>
        <input type='checkbox' id='setRts' onclick='onSetRts(this)' />
        <label>setRts</label>
    </div>

    <hr />

    <div>
        <input type='checkbox' id='isSendHex' />
        <label>isSendHex</label>
        <label>Send</label>
        <input id='send' type='text' style='width:230px;' value='https://github.com/itas109/CSerialPort'>
        <button id='onSend' onclick='onSend()'>send</button>
    </div>

    <hr />

    <div id='left'>
        <h2>Receive</h2>
        <input type='checkbox' id='isReceiveHex' onclick='onSetReceiveMode()' />
        <label>isReceiveHex</label>
        <button id='clearReceive' onclick='onClearReceive()'>clear</button>
        <pre id='receive'></pre>
    </div>

    <div id='right'>
        <h2>Log</h2>
        <button id='clearLog' onclick='onClear()'>clear</button>
        <pre id='log'></pre>
    </div>

    <script>
        // tile
        (async function listPort() {
            let result = await window.getVersion();
            document.title += ' - ' + result.version;
        })();

        // listPortSelect
        const listPortSelect = document.getElementById('listPortSelect');
        (async function listPort() {
            const listPorts = await window.listPorts();
            listPorts.forEach(listPort => {
                const optionElement = document.createElement('option');
                optionElement.value = listPort.portName;
                optionElement.textContent = listPort.portName;
                listPortSelect.appendChild(optionElement);
            });
        })();

        // baudRateSelect
        const baudRateSelect = document.getElementById('baudRateSelect');
        const baudRateArray = [110, 300, 600, 1200, 2400, 4800, 9600, 14400, 19200, 38400, 57600, 115200, 921600];
        baudRateArray.forEach(baudRate => {
            const option = document.createElement('option');
            option.value = baudRate;
            option.text = baudRate;
            if (baudRate === 9600) {
                option.selected = true; // default selected
            }
            baudRateSelect.appendChild(option);
        });
    </script>
    <script>
        function output(str, id = '#log') {
            document.querySelector(id).textContent += str + '\n';
        }

        function outputReceive(str, id = '#receive') {
            document.querySelector(id).textContent += str;
        }

        function onClear(id = '#log') {
            document.querySelector(id).textContent = '';
        }

        function onClearReceive(id = '#receive') {
            document.querySelector(id).textContent = '';
        }

        async function onOpen() {
            const params = {
                portName: document.querySelector('#listPortSelect option:checked').value,
                baudRate: parseInt(document.querySelector('#baudRateSelect option:checked').value)
            };

            await window.init(params);
            let result = await window.open({});
            output(JSON.stringify(result));
        }

        async function onClose() {
            await window.close({});
            output('close ok');
        }

        async function onSend() {
            const str = document.querySelector('#send').value;
            const params = {
                isHex: document.querySelector('#isSendHex').checked,
                data: encodeURI(str),
                size: str.length
            };
            let result;
            result = await window.writeData(params);
            output(JSON.stringify(result));
        }

        async function onSetReceiveMode() {
            const params = {
                isReceiveHex: document.querySelector('#isReceiveHex').checked
            };
            await window.setReceiveMode(params);
        }

        async function onSetDtr(checkbox) {
            await window.setDtr({
                isSet: checkbox.checked
            });
            output('setDtr ' + checkbox.checked);
        }

        async function onSetRts(checkbox) {
            await window.setRts({
                isSet: checkbox.checked
            });
            output('setRts ' + checkbox.checked);
        }
    </script>

</body>

</html>