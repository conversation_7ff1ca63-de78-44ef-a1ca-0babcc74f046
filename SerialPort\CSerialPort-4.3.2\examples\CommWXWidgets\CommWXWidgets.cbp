<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="6" />
	<Project>
		<Option title="CommWXWidgets" />
		<Option pch_mode="2" />
		<Option compiler="gcc" />
		<Build>
			<Target title="Debug">
				<Option output="bin/Debug/CommWXWidgets" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Debug/" />
				<Option type="0" />
				<Option compiler="gcc" />
				<Option projectLinkerOptionsRelation="2" />
				<Compiler>
					<Add option="-g" />
					<Add option="-D__WXDEBUG__" />
					<Add directory="$(#wx)/lib/gcc_dll/mswud" />
				</Compiler>
				<ResourceCompiler>
					<Add directory="$(#wx)/lib/gcc_dll/mswud" />
				</ResourceCompiler>
				<Linker>
					<Add library="libwxmsw31ud_richtext.a" />
					<Add library="libwxbase31ud_xml.a" />
					<Add library="libwxmsw31ud_adv.a" />
					<Add library="libwxmsw31ud_html.a" />
					<Add library="libwxmsw31ud_core.a" />
					<Add library="libwxbase31ud.a" />
					<Add library="libwxpngd.a" />
					<Add library="libwxzlibd.a" />
					<Add directory="$(#wx)/lib/gcc_dll" />
				</Linker>
			</Target>
			<Target title="Release">
				<Option output="bin/Release/CommWXWidgets" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Release/" />
				<Option type="0" />
				<Option compiler="gcc" />
				<Option projectLinkerOptionsRelation="2" />
				<Compiler>
					<Add option="-O2" />
					<Add directory="$(#wx)/lib/gcc_dll/mswu" />
				</Compiler>
				<ResourceCompiler>
					<Add directory="$(#wx)/lib/gcc_dll/mswu" />
				</ResourceCompiler>
				<Linker>
					<Add option="-s" />
					<Add library="libwxmsw31u_richtext.a" />
					<Add library="libwxbase31u_xml.a" />
					<Add library="libwxmsw31u_adv.a" />
					<Add library="libwxmsw31u_html.a" />
					<Add library="libwxmsw31u_core.a" />
					<Add library="libwxbase31u.a" />
					<Add library="libwxpng.a" />
					<Add library="libwxzlib.a" />
					<Add directory="$(#wx)/lib/gcc_dll" />
				</Linker>
			</Target>
		</Build>
		<Compiler>
			<Add option="-Wall" />
			<Add option="-pipe" />
			<Add option="-mthreads" />
			<Add option="-D__GNUWIN32__" />
			<Add option="-D__WXMSW__" />
			<Add option="-DWXUSINGDLL" />
			<Add option="-DwxUSE_UNICODE" />
			<Add directory="$(#wx)/include" />
			<Add directory="../../include" />
		</Compiler>
		<ResourceCompiler>
			<Add directory="$(#wx)/include" />
		</ResourceCompiler>
		<Linker>
			<Add option="-mthreads" />
			<Add library="setupapi" />
		</Linker>
		<Unit filename="../../src/SerialPort.cpp" />
		<Unit filename="../../src/SerialPortBase.cpp" />
		<Unit filename="../../src/SerialPortInfo.cpp" />
		<Unit filename="../../src/SerialPortInfoBase.cpp" />
		<Unit filename="../../src/SerialPortInfoWinBase.cpp" />
		<Unit filename="../../src/SerialPortWinBase.cpp" />
		<Unit filename="CommWXWidgetsApp.cpp" />
		<Unit filename="CommWXWidgetsApp.h" />
		<Unit filename="CommWXWidgetsMain.cpp" />
		<Unit filename="CommWXWidgetsMain.h" />
		<Unit filename="resource.rc">
			<Option compilerVar="WINDRES" />
		</Unit>
		<Unit filename="wxsmith/CommWXWidgetsdialog.wxs" />
		<Extensions>
			<wxsmith version="1">
				<gui name="wxWidgets" src="CommWXWidgetsApp.cpp" main="CommWXWidgetsDialog" init_handlers="necessary" language="CPP" />
				<resources>
					<wxDialog wxs="wxsmith/CommWXWidgetsdialog.wxs" src="CommWXWidgetsMain.cpp" hdr="CommWXWidgetsMain.h" fwddecl="0" i18n="1" name="CommWXWidgetsDialog" language="CPP" />
				</resources>
			</wxsmith>
		</Extensions>
	</Project>
</CodeBlocks_project_file>
