# Create C# Project Task

## Task Definition
- **ID**: create-csharp-project
- **Name**: Create New C# Project
- **Description**: Set up a new C# project with proper structure, dependencies, and configuration
- **Agent**: csharp-dev
- **Elicit**: true

## Prerequisites
- .NET SDK installed (version 6.0 or later)
- Development environment configured
- Project requirements defined

## Elicitation Questions
1. **Project Type**: What type of C# project do you want to create?
   - Console Application
   - Web API
   - MVC Web Application
   - Blazor Server/WebAssembly
   - WPF Application
   - WinForms Application
   - Class Library
   - Test Project

2. **Target Framework**: Which .NET version should we target?
   - .NET 8.0 (Latest LTS)
   - .NET 6.0 (LTS)
   - .NET Standard 2.1

3. **Project Name**: What is the name of your project?

4. **Solution Structure**: Do you need a solution file with multiple projects?

5. **Additional Features**: Do you need any of these features?
   - Entity Framework Core
   - Authentication/Authorization
   - Swagger/OpenAPI documentation
   - Logging framework (Serilog/NLog)
   - Configuration management
   - Dependency injection container
   - Unit testing setup

## Execution Steps

### Step 1: Create Project Structure
```bash
# Create solution (if needed)
dotnet new sln -n {{project_name}}

# Create main project
dotnet new {{project_template}} -n {{project_name}} -f {{target_framework}}

# Add project to solution (if solution exists)
dotnet sln add {{project_name}}/{{project_name}}.csproj
```

### Step 2: Configure Project File
Update the `.csproj` file with:
- Nullable reference types enabled
- Treat warnings as errors
- Latest language version
- Appropriate package references

### Step 3: Add Essential Packages
Based on project type, add relevant packages:
- Logging: `Microsoft.Extensions.Logging`
- Configuration: `Microsoft.Extensions.Configuration`
- DI: `Microsoft.Extensions.DependencyInjection`
- Testing: `xunit`, `Microsoft.NET.Test.Sdk`

### Step 4: Create Folder Structure
Organize code with appropriate folders:
- `Models/` - Data models
- `Services/` - Business logic
- `Controllers/` - API controllers (if web project)
- `Data/` - Data access layer
- `Tests/` - Unit tests

### Step 5: Add Configuration Files
- `appsettings.json` - Application settings
- `appsettings.Development.json` - Development settings
- `.gitignore` - Git ignore file
- `README.md` - Project documentation

### Step 6: Set Up Development Environment
- Configure debugging settings
- Set up launch profiles
- Configure development certificates (if web project)

## Validation Checklist
- [ ] Project builds successfully
- [ ] All dependencies resolve correctly
- [ ] Configuration files are properly structured
- [ ] Folder structure follows conventions
- [ ] Git repository is initialized (if requested)
- [ ] Development environment is configured
- [ ] Basic tests can be run (if test project included)

## Output
- Fully configured C# project
- Solution file (if requested)
- Documentation files
- Development environment setup
- Initial commit (if Git requested)
