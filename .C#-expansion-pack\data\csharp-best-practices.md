# C# Best Practices Guide

## Naming Conventions

### Classes and Interfaces
- Use PascalCase for class names: `CustomerService`, `OrderProcessor`
- Use PascalCase for interface names with 'I' prefix: `ICustomerService`, `IRepository<T>`
- Use descriptive names that clearly indicate purpose

### Methods and Properties
- Use PascalCase for method names: `GetCustomerById`, `ProcessOrder`
- Use PascalCase for property names: `FirstName`, `OrderDate`
- Use verb-noun combinations for methods: `CalculateTotal`, `ValidateInput`

### Variables and Parameters
- Use camelCase for local variables: `customerName`, `orderTotal`
- Use camelCase for method parameters: `customerId`, `orderDate`
- Use meaningful names, avoid abbreviations: `customer` not `cust`

### Fields
- Use camelCase with underscore prefix for private fields: `_customerService`, `_logger`
- Use PascalCase for constants: `MaxRetryCount`, `DefaultTimeout`

## Code Organization

### File Structure
- One class per file (with rare exceptions)
- File name matches class name
- Organize using statements alphabetically
- Remove unused using statements

### Namespace Organization
```csharp
// System namespaces first
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

// Third-party namespaces
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

// Project namespaces last
using MyProject.Core.Entities;
using MyProject.Core.Interfaces;
```

### Class Structure Order
1. Constants
2. Fields
3. Constructors
4. Properties
5. Methods (public first, then private)
6. Events
7. Nested types

## Error Handling

### Exception Best Practices
- Use specific exception types
- Create custom exceptions when needed
- Include meaningful error messages
- Don't swallow exceptions without logging

```csharp
// Good
try
{
    var customer = await _customerService.GetByIdAsync(id);
    return Ok(customer);
}
catch (CustomerNotFoundException ex)
{
    _logger.LogWarning(ex, "Customer not found: {CustomerId}", id);
    return NotFound($"Customer with ID {id} not found");
}
catch (Exception ex)
{
    _logger.LogError(ex, "Error retrieving customer: {CustomerId}", id);
    return StatusCode(500, "An error occurred while retrieving the customer");
}
```

### Custom Exceptions
```csharp
public class CustomerNotFoundException : Exception
{
    public int CustomerId { get; }

    public CustomerNotFoundException(int customerId) 
        : base($"Customer with ID {customerId} was not found")
    {
        CustomerId = customerId;
    }

    public CustomerNotFoundException(int customerId, Exception innerException) 
        : base($"Customer with ID {customerId} was not found", innerException)
    {
        CustomerId = customerId;
    }
}
```

## Async/Await Patterns

### Best Practices
- Always use async/await for I/O operations
- Add 'Async' suffix to async method names
- Return Task or Task<T> from async methods
- Use ConfigureAwait(false) in library code
- Never use async void (except event handlers)

```csharp
// Good
public async Task<Customer> GetCustomerAsync(int id)
{
    var customer = await _repository.GetByIdAsync(id).ConfigureAwait(false);
    return customer;
}

// Bad - blocking async code
public Customer GetCustomer(int id)
{
    return _repository.GetByIdAsync(id).Result; // Don't do this!
}
```

### Cancellation Tokens
```csharp
public async Task<IEnumerable<Customer>> GetCustomersAsync(CancellationToken cancellationToken = default)
{
    return await _repository.GetAllAsync(cancellationToken);
}
```

## Memory Management

### IDisposable Pattern
```csharp
public class CustomerService : IDisposable
{
    private readonly HttpClient _httpClient;
    private bool _disposed = false;

    public CustomerService()
    {
        _httpClient = new HttpClient();
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _httpClient?.Dispose();
        }
        _disposed = true;
    }
}
```

### Using Statements
```csharp
// Preferred - using declaration (C# 8+)
using var client = new HttpClient();
var response = await client.GetAsync(url);

// Traditional using statement
using (var client = new HttpClient())
{
    var response = await client.GetAsync(url);
}
```

## LINQ Best Practices

### Efficient Queries
```csharp
// Good - single enumeration
var activeCustomers = customers
    .Where(c => c.IsActive)
    .ToList();

// Bad - multiple enumerations
var activeCustomers = customers.Where(c => c.IsActive);
var count = activeCustomers.Count(); // Enumerates again
var list = activeCustomers.ToList(); // Enumerates again
```

### Deferred Execution
```csharp
// Deferred execution - query not executed until enumerated
var query = customers.Where(c => c.IsActive);

// Immediate execution
var list = customers.Where(c => c.IsActive).ToList();
```

## Dependency Injection

### Constructor Injection
```csharp
public class CustomerController : ControllerBase
{
    private readonly ICustomerService _customerService;
    private readonly ILogger<CustomerController> _logger;

    public CustomerController(
        ICustomerService customerService,
        ILogger<CustomerController> logger)
    {
        _customerService = customerService ?? throw new ArgumentNullException(nameof(customerService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }
}
```

### Service Registration
```csharp
// Program.cs or Startup.cs
services.AddScoped<ICustomerService, CustomerService>();
services.AddSingleton<IConfiguration>(configuration);
services.AddTransient<IEmailService, EmailService>();
```

## Performance Considerations

### String Operations
```csharp
// Good for multiple concatenations
var sb = new StringBuilder();
foreach (var item in items)
{
    sb.AppendLine(item.ToString());
}
var result = sb.ToString();

// Good for few concatenations
var result = $"Hello {name}, today is {DateTime.Now:yyyy-MM-dd}";
```

### Collection Initialization
```csharp
// Good - specify capacity if known
var list = new List<Customer>(expectedCount);

// Good - collection initializer
var statuses = new HashSet<string> { "Active", "Inactive", "Pending" };
```

## Security Best Practices

### Input Validation
```csharp
public async Task<ActionResult<Customer>> CreateCustomer([FromBody] CreateCustomerRequest request)
{
    if (string.IsNullOrWhiteSpace(request.Name))
    {
        return BadRequest("Customer name is required");
    }

    if (request.Email != null && !IsValidEmail(request.Email))
    {
        return BadRequest("Invalid email format");
    }

    // Process request...
}
```

### SQL Injection Prevention
```csharp
// Good - parameterized query
var customers = await _context.Customers
    .Where(c => c.Name == customerName)
    .ToListAsync();

// Bad - string concatenation
var sql = $"SELECT * FROM Customers WHERE Name = '{customerName}'"; // Don't do this!
```

## Testing Best Practices

### Unit Test Structure (AAA Pattern)
```csharp
[Fact]
public async Task GetCustomerAsync_WithValidId_ReturnsCustomer()
{
    // Arrange
    var customerId = 1;
    var expectedCustomer = new Customer { Id = customerId, Name = "John Doe" };
    _mockRepository.Setup(r => r.GetByIdAsync(customerId))
                   .ReturnsAsync(expectedCustomer);

    // Act
    var result = await _customerService.GetCustomerAsync(customerId);

    // Assert
    Assert.NotNull(result);
    Assert.Equal(expectedCustomer.Id, result.Id);
    Assert.Equal(expectedCustomer.Name, result.Name);
}
```

### Test Naming Convention
- `MethodName_Scenario_ExpectedResult`
- `GetCustomerAsync_WithValidId_ReturnsCustomer`
- `CreateCustomer_WithInvalidData_ThrowsValidationException`

## Configuration Management

### appsettings.json Structure
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=MyApp;Trusted_Connection=true;"
  },
  "AppSettings": {
    "MaxRetryCount": 3,
    "TimeoutSeconds": 30
  }
}
```

### Strongly Typed Configuration
```csharp
public class AppSettings
{
    public int MaxRetryCount { get; set; }
    public int TimeoutSeconds { get; set; }
}

// Registration
services.Configure<AppSettings>(configuration.GetSection("AppSettings"));

// Usage
public class SomeService
{
    private readonly AppSettings _settings;

    public SomeService(IOptions<AppSettings> settings)
    {
        _settings = settings.Value;
    }
}
```
