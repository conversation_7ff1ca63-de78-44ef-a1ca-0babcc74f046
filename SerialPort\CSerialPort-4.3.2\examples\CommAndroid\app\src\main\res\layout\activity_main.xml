<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <Spinner
        android:id="@+id/spinnerPortName"
        android:layout_width="182dp"
        android:layout_height="46dp"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@+id/recyclerView"
        tools:layout_editor_absoluteX="16dp" />

    <Button
        android:id="@+id/buttonRefreshPort"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:text="@string/refreshPort"
        app:layout_constraintBottom_toBottomOf="@id/spinnerPortName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@id/spinnerPortName"
        app:layout_constraintTop_toTopOf="@id/spinnerPortName" />

    <Button
        android:id="@+id/buttonOpenPort"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:text="@string/openPort"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/spinnerPortName" />

    <Button
        android:id="@+id/buttonClosePort"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:text="@string/closePort"
        app:layout_constraintBottom_toBottomOf="@id/buttonOpenPort"
        app:layout_constraintStart_toEndOf="@id/buttonOpenPort"
        app:layout_constraintTop_toTopOf="@id/buttonOpenPort" />

    <Button
        android:id="@+id/buttonSend"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:text="@string/send"
        app:layout_constraintBottom_toBottomOf="@id/buttonClosePort"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.012"
        app:layout_constraintStart_toEndOf="@id/buttonClosePort"
        app:layout_constraintTop_toTopOf="@id/buttonClosePort"
        app:layout_constraintVertical_bias="1.0" />

    <EditText
        android:id="@+id/editTextReceive"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:inputType="textMultiLine"
        android:scrollbars="vertical"
        app:layout_constraintTop_toBottomOf="@id/buttonOpenPort"/>

</androidx.constraintlayout.widget.ConstraintLayout>